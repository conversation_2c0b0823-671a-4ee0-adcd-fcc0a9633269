import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:signage/utils/platform_utils.dart';
import 'package:flutter_libserialport/flutter_libserialport.dart' if (dart.library.html) 'dart:html';
import 'package:usb_serial/usb_serial.dart' if (dart.library.html) 'dart:html';

/// Protocol-compliant data structure for LD2410B [1]
class LD2410Data {
  final int movingTargetDistance;
  final int movingTargetEnergy;
  final int staticTargetDistance;
  final int staticTargetEnergy;
  final int detectionDistance;
  final DetectionState detectionState;

  LD2410Data({
    required this.movingTargetDistance,
    required this.movingTargetEnergy,
    required this.staticTargetDistance,
    required this.staticTargetEnergy,
    required this.detectionDistance,
    required this.detectionState,
  });

  @override
  String toString() {
    return 'Detection: ${detectionState.name} | '
           'Distance: ${detectionDistance}cm | '
           'Moving: ${movingTargetDistance}cm (${movingTargetEnergy}%) | '
           'Static: ${staticTargetDistance}cm (${staticTargetEnergy}%)';
  }
}

enum DetectionState { none, moving, static, combined }

class SerialCommunicationService {
  static final _instance = SerialCommunicationService._internal();
  factory SerialCommunicationService() => _instance;
  SerialCommunicationService._internal();

  SerialPort? _desktopPort;
  UsbPort? _androidPort;
  bool _isConnected = false;
  StreamController<LD2410Data>? _dataController;
  final List<int> _buffer = [];
  static const _header = [0xF4, 0xF3, 0x02];

  Stream<LD2410Data>? get dataStream => _dataController?.stream;

  Future<void> initialize() async {
    if (_dataController != null) return;
    _dataController = StreamController.broadcast();
    
    try {
      if (PlatformUtils.isAndroid) {
        await _initializeAndroid();
      } else if (PlatformUtils.isDesktop) {
        await _initializeDesktop();
      }
    } catch (e) {
      debugPrint('Initialization error: $e');
    }
  }

  Future<void> _initializeAndroid() async {
    final devices = await UsbSerial.listDevices();
    final device = devices.firstWhere(
      (d) => d.vid == 0x1A86 && d.pid == 0x7523,
      orElse: () => throw 'LD2410B device not found'
    );

    _androidPort = await device.create();
    await _androidPort?.open();
    await _androidPort?.setPortParameters(115200, 8, 1, UsbPort.PARITY_NONE);
    
    _androidPort?.inputStream?.listen((data) => _processData(Uint8List.fromList(data)));
    _isConnected = true;
  }

  Future<void> _initializeDesktop() async {
    final ports = await SerialPort.availablePorts;
    final portName = ports.firstWhere(
      (p) => _isLD2410BDevice(p),
      orElse: () => throw 'LD2410B device not found'
    );

    _desktopPort = SerialPort(portName);
    _desktopPort?.openReadWrite();
    _desktopPort?.config = SerialPortConfig()
      ..baudRate = 115200
      ..bits = 8
      ..stopBits = 1
      ..parity = SerialPortParity.none;

    SerialPortReader(_desktopPort!).stream.listen(
      (data) => _processData(Uint8List.fromList(data))
    );
    _isConnected = true;
  }

  void _processData(Uint8List data) {
    _buffer.addAll(data);
    
    while (_buffer.length >= 14) {
      final frame = _buffer.sublist(0, 14);
      if (_validateFrame(frame)) {
        final parsed = _parseDataFrame(frame);
        if (parsed != null) _dataController?.add(parsed);
        _buffer.removeRange(0, 14);
      } else {
        _buffer.removeAt(0);
      }
    }
  }

  bool _validateFrame(List<int> frame) {
    if (frame[0] != _header[0] || frame[1] != _header[1] || frame[2] != _header[2]) return false;
    
    final checksum = frame.sublist(0, 13)
      .fold<int>(0, (sum, byte) => sum + byte) & 0xFF;
    return checksum == frame[13];
  }

  LD2410Data? _parseDataFrame(List<int> frame) {
    try {
      return LD2410Data(
        movingTargetDistance: frame[4] | (frame[5] << 8),
        movingTargetEnergy: frame[6],
        staticTargetDistance: frame[7] | (frame[8] << 8),
        staticTargetEnergy: frame[9],
        detectionDistance: frame[10] | (frame[11] << 8),
        detectionState: DetectionState.values[frame[12]],
      );
    } catch (e) {
      debugPrint('Parsing error: $e');
      return null;
    }
  }

  bool _isLD2410BDevice(String portName) {
    final port = SerialPort(portName);
    return port.vendorId == 0x1A86 && port.productId == 0x7523;
  }

  Future<void> dispose() async {
    await _androidPort?.close();
    _desktopPort?.close();
    await _dataController?.close();
    _buffer.clear();
    _isConnected = false;
  }
}
