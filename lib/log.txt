Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on Linux is available at: http://127.0.0.1:33833/UJ2_he27qzk=/
The Flutter DevTools debugger and profiler on Linux is available at:
http://127.0.0.1:9101?uri=http://127.0.0.1:33833/UJ2_he27qzk=/
flutter: supabase.supabase_flutter: INFO: ***** Supabase init completed ***** 
flutter: media_kit: NativeReferenceHolder: Allocated 129934237810416
flutter: Created signage directory structure at: /home/<USER>/Documents/signage
flutter: Content directory: /home/<USER>/Documents/signage/content
flutter: Data directory: /home/<USER>/Documents/signage/data
flutter: Temp directory: /home/<USER>/Documents/signage/temp
flutter: CampaignController: Found 1 proximity campaigns before filtering
flutter: CampaignController: After filtering: 1 non-proximity campaigns
flutter: CampaignController: Setting up proximity service callback
flutter: CampaignController: Setting sensor data callback on singleton instance
flutter: AudienceProximityService: Setting sensor data callback
flutter: CampaignController: Sensor data callback set successfully
flutter: AudienceProximityService: Initializing
flutter: AudienceProximityService: Found proximity campaign: Broadband Compare Promo
flutter: AudienceProximityService: Proximity campaigns found, initializing serial service
media_kit: VideoOutput: video_output_new: 129934240141936
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
flutter: VideoOutput.Resize
flutter: {handle: 129934240141936, id: 94030621555632, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 94030621555632
media_kit: TextureGL: Resize: (1920, 540)
flutter: VideoOutput.Resize
flutter: {handle: 129934240141936, id: 94030621555632, rect: {left: 0, top: 0, width: 1920, height: 540}}
flutter: AudienceProximityService: Serial service initialized successfully
flutter: AudienceProximityService: Starting distance monitoring
flutter: AudienceProximityService: Distance monitoring subscription created successfully
flutter: AudienceProximityService: Sensor data received - Distance: 1.16m (116cm)
flutter: AudienceProximityService: Calling callback with distance: 1.16m
flutter: CampaignController: Received sensor data: 1.16
flutter: AudienceProximityService: Sensor data received - Distance: 1.15m (115cm)
flutter: AudienceProximityService: Calling callback with distance: 1.15m
flutter: CampaignController: Received sensor data: 1.15
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.2m (120cm)
flutter: AudienceProximityService: Calling callback with distance: 1.2m
flutter: CampaignController: Received sensor data: 1.2
flutter: AudienceProximityService: Sensor data received - Distance: 1.18m (118cm)
flutter: AudienceProximityService: Calling callback with distance: 1.18m
flutter: CampaignController: Received sensor data: 1.18
flutter: AudienceProximityService: Sensor data received - Distance: 1.18m (118cm)
flutter: AudienceProximityService: Calling callback with distance: 1.18m
flutter: CampaignController: Received sensor data: 1.18
flutter: AudienceProximityService: Sensor data received - Distance: 1.18m (118cm)
flutter: AudienceProximityService: Calling callback with distance: 1.18m
flutter: CampaignController: Received sensor data: 1.18
flutter: AudienceProximityService: Sensor data received - Distance: 1.18m (118cm)
flutter: AudienceProximityService: Calling callback with distance: 1.18m
flutter: CampaignController: Received sensor data: 1.18
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.21m (121cm)
flutter: AudienceProximityService: Calling callback with distance: 1.21m
flutter: CampaignController: Received sensor data: 1.21
flutter: AudienceProximityService: Sensor data received - Distance: 1.22m (122cm)
flutter: AudienceProximityService: Calling callback with distance: 1.22m
flutter: CampaignController: Received sensor data: 1.22
flutter: AudienceProximityService: Sensor data received - Distance: 1.21m (121cm)
flutter: AudienceProximityService: Calling callback with distance: 1.21m
flutter: CampaignController: Received sensor data: 1.21
flutter: AudienceProximityService: Sensor data received - Distance: 1.2m (120cm)
flutter: AudienceProximityService: Calling callback with distance: 1.2m
flutter: CampaignController: Received sensor data: 1.2
flutter: AudienceProximityService: Sensor data received - Distance: 1.21m (121cm)
flutter: AudienceProximityService: Calling callback with distance: 1.21m
flutter: CampaignController: Received sensor data: 1.21
flutter: AudienceProximityService: Sensor data received - Distance: 1.21m (121cm)
flutter: AudienceProximityService: Calling callback with distance: 1.21m
flutter: CampaignController: Received sensor data: 1.21
flutter: AudienceProximityService: Sensor data received - Distance: 1.2m (120cm)
flutter: AudienceProximityService: Calling callback with distance: 1.2m
flutter: CampaignController: Received sensor data: 1.2
flutter: AudienceProximityService: Sensor data received - Distance: 1.18m (118cm)
flutter: AudienceProximityService: Calling callback with distance: 1.18m
flutter: CampaignController: Received sensor data: 1.18
flutter: AudienceProximityService: Sensor data received - Distance: 1.17m (117cm)
flutter: AudienceProximityService: Calling callback with distance: 1.17m
flutter: CampaignController: Received sensor data: 1.17
flutter: AudienceProximityService: Sensor data received - Distance: 1.17m (117cm)
flutter: AudienceProximityService: Calling callback with distance: 1.17m
flutter: CampaignController: Received sensor data: 1.17
flutter: AudienceProximityService: Sensor data received - Distance: 1.18m (118cm)
flutter: AudienceProximityService: Calling callback with distance: 1.18m
flutter: CampaignController: Received sensor data: 1.18
flutter: AudienceProximityService: Sensor data received - Distance: 1.2m (120cm)
flutter: AudienceProximityService: Calling callback with distance: 1.2m
flutter: CampaignController: Received sensor data: 1.2
flutter: AudienceProximityService: Sensor data received - Distance: 1.21m (121cm)
flutter: AudienceProximityService: Calling callback with distance: 1.21m
flutter: CampaignController: Received sensor data: 1.21
flutter: AudienceProximityService: Sensor data received - Distance: 1.22m (122cm)
flutter: AudienceProximityService: Calling callback with distance: 1.22m
flutter: CampaignController: Received sensor data: 1.22
flutter: AudienceProximityService: Sensor data received - Distance: 1.22m (122cm)
flutter: AudienceProximityService: Calling callback with distance: 1.22m
flutter: CampaignController: Received sensor data: 1.22
flutter: AudienceProximityService: Sensor data received - Distance: 1.27m (127cm)
flutter: AudienceProximityService: Calling callback with distance: 1.27m
flutter: CampaignController: Received sensor data: 1.27
flutter: AudienceProximityService: Sensor data received - Distance: 1.26m (126cm)
flutter: AudienceProximityService: Calling callback with distance: 1.26m
flutter: CampaignController: Received sensor data: 1.26
flutter: AudienceProximityService: Sensor data received - Distance: 1.23m (123cm)
flutter: AudienceProximityService: Calling callback with distance: 1.23m
flutter: CampaignController: Received sensor data: 1.23
flutter: AudienceProximityService: Sensor data received - Distance: 1.23m (123cm)
flutter: AudienceProximityService: Calling callback with distance: 1.23m
flutter: CampaignController: Received sensor data: 1.23
flutter: AudienceProximityService: Sensor data received - Distance: 1.19m (119cm)
flutter: AudienceProximityService: Calling callback with distance: 1.19m
flutter: CampaignController: Received sensor data: 1.19
flutter: AudienceProximityService: Sensor data received - Distance: 1.2m (120cm)
flutter: AudienceProximityService: Calling callback with distance: 1.2m
flutter: CampaignController: Received sensor data: 1.2
flutter: AudienceProximityService: Sensor data received - Distance: 1.2m (120cm)
flutter: AudienceProximityService: Calling callback with distance: 1.2m
flutter: CampaignController: Received sensor data: 1.2
flutter: AudienceProximityService: Sensor data received - Distance: 1.16m (116cm)
flutter: AudienceProximityService: Calling callback with distance: 1.16m
flutter: CampaignController: Received sensor data: 1.16
flutter: AudienceProximityService: Sensor data received - Distance: 1.15m (115cm)
flutter: AudienceProximityService: Calling callback with distance: 1.15m
flutter: CampaignController: Received sensor data: 1.15
flutter: AudienceProximityService: Sensor data received - Distance: 1.09m (109cm)
flutter: AudienceProximityService: Calling callback with distance: 1.09m
flutter: CampaignController: Received sensor data: 1.09
flutter: AudienceProximityService: Sensor data received - Distance: 1.04m (104cm)
flutter: AudienceProximityService: Calling callback with distance: 1.04m
flutter: CampaignController: Received sensor data: 1.04
flutter: AudienceProximityService: Sensor data received - Distance: 0.98m (98cm)
flutter: AudienceProximityService: Calling callback with distance: 0.98m
flutter: CampaignController: Received sensor data: 0.98
flutter: AudienceProximityService: Sensor data received - Distance: 0.94m (94cm)
flutter: AudienceProximityService: Calling callback with distance: 0.94m
flutter: CampaignController: Received sensor data: 0.94
flutter: AudienceProximityService: Sensor data received - Distance: 0.91m (91cm)
flutter: AudienceProximityService: Calling callback with distance: 0.91m
flutter: CampaignController: Received sensor data: 0.91
flutter: AudienceProximityService: Sensor data received - Distance: 0.87m (87cm)
flutter: AudienceProximityService: Calling callback with distance: 0.87m
flutter: CampaignController: Received sensor data: 0.87
media_kit: VideoOutput: video_output_new: 129934238721664
flutter: AudienceProximityService: Sensor data received - Distance: 0.85m (85cm)
flutter: AudienceProximityService: Calling callback with distance: 0.85m
flutter: CampaignController: Received sensor data: 0.85
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
flutter: VideoOutput.Resize
flutter: {handle: 129934238721664, id: 94030622785216, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 94030622785216
flutter: AudienceProximityService: Sensor data received - Distance: 0.87m (87cm)
flutter: AudienceProximityService: Calling callback with distance: 0.87m
flutter: CampaignController: Received sensor data: 0.87
flutter: AudienceProximityService: Sensor data received - Distance: 0.9m (90cm)
flutter: AudienceProximityService: Calling callback with distance: 0.9m
flutter: CampaignController: Received sensor data: 0.9
flutter: AudienceProximityService: Sensor data received - Distance: 0.86m (86cm)
flutter: AudienceProximityService: Calling callback with distance: 0.86m
flutter: CampaignController: Received sensor data: 0.86
flutter: AudienceProximityService: Sensor data received - Distance: 0.84m (84cm)
flutter: AudienceProximityService: Calling callback with distance: 0.84m
flutter: CampaignController: Received sensor data: 0.84
media_kit: VideoOutput: video_output_dispose: 129934240141936
media_kit: TextureGL: Resize: (1920, 540)
flutter: VideoOutput.Resize
flutter: {handle: 129934238721664, id: 94030622785216, rect: {left: 0, top: 0, width: 1920, height: 540}}
flutter: AudienceProximityService: Sensor data received - Distance: 0.84m (84cm)
flutter: AudienceProximityService: Calling callback with distance: 0.84m
flutter: CampaignController: Received sensor data: 0.84
flutter: AudienceProximityService: Sensor data received - Distance: 0.9m (90cm)
flutter: AudienceProximityService: Calling callback with distance: 0.9m
flutter: CampaignController: Received sensor data: 0.9
flutter: AudienceProximityService: Sensor data received - Distance: 0.87m (87cm)
flutter: AudienceProximityService: Calling callback with distance: 0.87m
flutter: CampaignController: Received sensor data: 0.87
flutter: AudienceProximityService: Sensor data received - Distance: 0.91m (91cm)
flutter: AudienceProximityService: Calling callback with distance: 0.91m
flutter: CampaignController: Received sensor data: 0.91
flutter: AudienceProximityService: Sensor data received - Distance: 0.94m (94cm)
flutter: AudienceProximityService: Calling callback with distance: 0.94m
flutter: CampaignController: Received sensor data: 0.94
flutter: AudienceProximityService: Sensor data received - Distance: 0.9m (90cm)
flutter: AudienceProximityService: Calling callback with distance: 0.9m
flutter: CampaignController: Received sensor data: 0.9
flutter: AudienceProximityService: Sensor data received - Distance: 0.88m (88cm)
flutter: AudienceProximityService: Calling callback with distance: 0.88m
flutter: CampaignController: Received sensor data: 0.88
flutter: AudienceProximityService: Sensor data received - Distance: 0.85m (85cm)
flutter: AudienceProximityService: Calling callback with distance: 0.85m
flutter: CampaignController: Received sensor data: 0.85
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.82m (82cm)
flutter: AudienceProximityService: Calling callback with distance: 0.82m
flutter: CampaignController: Received sensor data: 0.82
flutter: AudienceProximityService: Sensor data received - Distance: 0.79m (79cm)
flutter: AudienceProximityService: Calling callback with distance: 0.79m
flutter: CampaignController: Received sensor data: 0.79
flutter: AudienceProximityService: Sensor data received - Distance: 0.76m (76cm)
flutter: AudienceProximityService: Calling callback with distance: 0.76m
flutter: CampaignController: Received sensor data: 0.76
flutter: AudienceProximityService: Sensor data received - Distance: 0.76m (76cm)
flutter: AudienceProximityService: Calling callback with distance: 0.76m
flutter: CampaignController: Received sensor data: 0.76
flutter: AudienceProximityService: Sensor data received - Distance: 0.84m (84cm)
flutter: AudienceProximityService: Calling callback with distance: 0.84m
flutter: CampaignController: Received sensor data: 0.84
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.78m (78cm)
flutter: AudienceProximityService: Calling callback with distance: 0.78m
flutter: CampaignController: Received sensor data: 0.78
flutter: AudienceProximityService: Sensor data received - Distance: 0.77m (77cm)
flutter: AudienceProximityService: Calling callback with distance: 0.77m
flutter: CampaignController: Received sensor data: 0.77
flutter: AudienceProximityService: Sensor data received - Distance: 0.78m (78cm)
flutter: AudienceProximityService: Calling callback with distance: 0.78m
flutter: CampaignController: Received sensor data: 0.78
flutter: AudienceProximityService: Sensor data received - Distance: 0.79m (79cm)
flutter: AudienceProximityService: Calling callback with distance: 0.79m
flutter: CampaignController: Received sensor data: 0.79
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.79m (79cm)
flutter: AudienceProximityService: Calling callback with distance: 0.79m
flutter: CampaignController: Received sensor data: 0.79
flutter: AudienceProximityService: Sensor data received - Distance: 0.79m (79cm)
flutter: AudienceProximityService: Calling callback with distance: 0.79m
flutter: CampaignController: Received sensor data: 0.79
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.84m (84cm)
flutter: AudienceProximityService: Calling callback with distance: 0.84m
flutter: CampaignController: Received sensor data: 0.84
flutter: AudienceProximityService: Sensor data received - Distance: 0.9m (90cm)
flutter: AudienceProximityService: Calling callback with distance: 0.9m
flutter: CampaignController: Received sensor data: 0.9
flutter: AudienceProximityService: Sensor data received - Distance: 0.9m (90cm)
flutter: AudienceProximityService: Calling callback with distance: 0.9m
flutter: CampaignController: Received sensor data: 0.9
flutter: AudienceProximityService: Sensor data received - Distance: 0.86m (86cm)
flutter: AudienceProximityService: Calling callback with distance: 0.86m
flutter: CampaignController: Received sensor data: 0.86
flutter: AudienceProximityService: Sensor data received - Distance: 0.82m (82cm)
flutter: AudienceProximityService: Calling callback with distance: 0.82m
flutter: CampaignController: Received sensor data: 0.82
flutter: AudienceProximityService: Sensor data received - Distance: 0.85m (85cm)
flutter: AudienceProximityService: Calling callback with distance: 0.85m
flutter: CampaignController: Received sensor data: 0.85
flutter: AudienceProximityService: Sensor data received - Distance: 0.86m (86cm)
flutter: AudienceProximityService: Calling callback with distance: 0.86m
flutter: CampaignController: Received sensor data: 0.86
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.8m (80cm)
flutter: AudienceProximityService: Calling callback with distance: 0.8m
flutter: CampaignController: Received sensor data: 0.8
flutter: AudienceProximityService: Sensor data received - Distance: 0.77m (77cm)
flutter: AudienceProximityService: Calling callback with distance: 0.77m
flutter: CampaignController: Received sensor data: 0.77
flutter: AudienceProximityService: Sensor data received - Distance: 0.75m (75cm)
flutter: AudienceProximityService: Calling callback with distance: 0.75m
flutter: CampaignController: Received sensor data: 0.75
flutter: AudienceProximityService: Sensor data received - Distance: 0.72m (72cm)
flutter: AudienceProximityService: Calling callback with distance: 0.72m
flutter: CampaignController: Received sensor data: 0.72
flutter: AudienceProximityService: Sensor data received - Distance: 0.7m (70cm)
flutter: AudienceProximityService: Calling callback with distance: 0.7m
flutter: CampaignController: Received sensor data: 0.7
flutter: AudienceProximityService: Sensor data received - Distance: 0.7m (70cm)
flutter: AudienceProximityService: Calling callback with distance: 0.7m
flutter: CampaignController: Received sensor data: 0.7
flutter: AudienceProximityService: Sensor data received - Distance: 0.73m (73cm)
flutter: AudienceProximityService: Calling callback with distance: 0.73m
flutter: CampaignController: Received sensor data: 0.73
flutter: AudienceProximityService: Sensor data received - Distance: 0.73m (73cm)
flutter: AudienceProximityService: Calling callback with distance: 0.73m
flutter: CampaignController: Received sensor data: 0.73
flutter: AudienceProximityService: Sensor data received - Distance: 0.75m (75cm)
flutter: AudienceProximityService: Calling callback with distance: 0.75m
flutter: CampaignController: Received sensor data: 0.75
flutter: AudienceProximityService: Sensor data received - Distance: 0.79m (79cm)
flutter: AudienceProximityService: Calling callback with distance: 0.79m
flutter: CampaignController: Received sensor data: 0.79
flutter: AudienceProximityService: Sensor data received - Distance: 0.78m (78cm)
flutter: AudienceProximityService: Calling callback with distance: 0.78m
flutter: CampaignController: Received sensor data: 0.78
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.86m (86cm)
flutter: AudienceProximityService: Calling callback with distance: 0.86m
flutter: CampaignController: Received sensor data: 0.86
flutter: AudienceProximityService: Sensor data received - Distance: 0.87m (87cm)
flutter: AudienceProximityService: Calling callback with distance: 0.87m
flutter: CampaignController: Received sensor data: 0.87
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.82m (82cm)
flutter: AudienceProximityService: Calling callback with distance: 0.82m
flutter: CampaignController: Received sensor data: 0.82
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.8m (80cm)
flutter: AudienceProximityService: Calling callback with distance: 0.8m
flutter: CampaignController: Received sensor data: 0.8
flutter: AudienceProximityService: Sensor data received - Distance: 0.8m (80cm)
flutter: AudienceProximityService: Calling callback with distance: 0.8m
flutter: CampaignController: Received sensor data: 0.8
flutter: AudienceProximityService: Sensor data received - Distance: 0.8m (80cm)
flutter: AudienceProximityService: Calling callback with distance: 0.8m
flutter: CampaignController: Received sensor data: 0.8
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.88m (88cm)
flutter: AudienceProximityService: Calling callback with distance: 0.88m
flutter: CampaignController: Received sensor data: 0.88
flutter: AudienceProximityService: Sensor data received - Distance: 0.85m (85cm)
flutter: AudienceProximityService: Calling callback with distance: 0.85m
flutter: CampaignController: Received sensor data: 0.85
flutter: AudienceProximityService: Sensor data received - Distance: 0.86m (86cm)
flutter: AudienceProximityService: Calling callback with distance: 0.86m
flutter: CampaignController: Received sensor data: 0.86
flutter: AudienceProximityService: Sensor data received - Distance: 0.85m (85cm)
flutter: AudienceProximityService: Calling callback with distance: 0.85m
flutter: CampaignController: Received sensor data: 0.85
flutter: AudienceProximityService: Sensor data received - Distance: 0.84m (84cm)
flutter: AudienceProximityService: Calling callback with distance: 0.84m
flutter: CampaignController: Received sensor data: 0.84
flutter: AudienceProximityService: Sensor data received - Distance: 0.82m (82cm)
flutter: AudienceProximityService: Calling callback with distance: 0.82m
flutter: CampaignController: Received sensor data: 0.82
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.88m (88cm)
flutter: AudienceProximityService: Calling callback with distance: 0.88m
flutter: CampaignController: Received sensor data: 0.88
flutter: AudienceProximityService: Sensor data received - Distance: 0.93m (93cm)
flutter: AudienceProximityService: Calling callback with distance: 0.93m
flutter: CampaignController: Received sensor data: 0.93
flutter: AudienceProximityService: Sensor data received - Distance: 1.0m (100cm)
flutter: AudienceProximityService: Calling callback with distance: 1.0m
flutter: CampaignController: Received sensor data: 1.0
flutter: AudienceProximityService: Sensor data received - Distance: 1.03m (103cm)
flutter: AudienceProximityService: Calling callback with distance: 1.03m
flutter: CampaignController: Received sensor data: 1.03
flutter: AudienceProximityService: Sensor data received - Distance: 1.03m (103cm)
flutter: AudienceProximityService: Calling callback with distance: 1.03m
flutter: CampaignController: Received sensor data: 1.03
flutter: AudienceProximityService: Sensor data received - Distance: 1.04m (104cm)
flutter: AudienceProximityService: Calling callback with distance: 1.04m
flutter: CampaignController: Received sensor data: 1.04
flutter: AudienceProximityService: Sensor data received - Distance: 1.02m (102cm)
flutter: AudienceProximityService: Calling callback with distance: 1.02m
flutter: CampaignController: Received sensor data: 1.02
flutter: AudienceProximityService: Sensor data received - Distance: 0.98m (98cm)
flutter: AudienceProximityService: Calling callback with distance: 0.98m
flutter: CampaignController: Received sensor data: 0.98
flutter: AudienceProximityService: Sensor data received - Distance: 0.96m (96cm)
flutter: AudienceProximityService: Calling callback with distance: 0.96m
flutter: CampaignController: Received sensor data: 0.96
flutter: AudienceProximityService: Sensor data received - Distance: 0.94m (94cm)
flutter: AudienceProximityService: Calling callback with distance: 0.94m
flutter: CampaignController: Received sensor data: 0.94
flutter: AudienceProximityService: Sensor data received - Distance: 1.01m (101cm)
flutter: AudienceProximityService: Calling callback with distance: 1.01m
flutter: CampaignController: Received sensor data: 1.01
flutter: AudienceProximityService: Sensor data received - Distance: 0.95m (95cm)
flutter: AudienceProximityService: Calling callback with distance: 0.95m
flutter: CampaignController: Received sensor data: 0.95
flutter: AudienceProximityService: Sensor data received - Distance: 0.92m (92cm)
flutter: AudienceProximityService: Calling callback with distance: 0.92m
flutter: CampaignController: Received sensor data: 0.92
flutter: AudienceProximityService: Sensor data received - Distance: 0.92m (92cm)
flutter: AudienceProximityService: Calling callback with distance: 0.92m
flutter: CampaignController: Received sensor data: 0.92
flutter: AudienceProximityService: Sensor data received - Distance: 0.92m (92cm)
flutter: AudienceProximityService: Calling callback with distance: 0.92m
flutter: CampaignController: Received sensor data: 0.92
flutter: AudienceProximityService: Sensor data received - Distance: 0.92m (92cm)
flutter: AudienceProximityService: Calling callback with distance: 0.92m
flutter: CampaignController: Received sensor data: 0.92
flutter: AudienceProximityService: Sensor data received - Distance: 0.93m (93cm)
flutter: AudienceProximityService: Calling callback with distance: 0.93m
flutter: CampaignController: Received sensor data: 0.93
flutter: AudienceProximityService: Sensor data received - Distance: 0.94m (94cm)
flutter: AudienceProximityService: Calling callback with distance: 0.94m
flutter: CampaignController: Received sensor data: 0.94
flutter: AudienceProximityService: Sensor data received - Distance: 0.95m (95cm)
flutter: AudienceProximityService: Calling callback with distance: 0.95m
flutter: CampaignController: Received sensor data: 0.95
flutter: AudienceProximityService: Sensor data received - Distance: 0.95m (95cm)
flutter: AudienceProximityService: Calling callback with distance: 0.95m
flutter: CampaignController: Received sensor data: 0.95
flutter: AudienceProximityService: Sensor data received - Distance: 0.91m (91cm)
flutter: AudienceProximityService: Calling callback with distance: 0.91m
flutter: CampaignController: Received sensor data: 0.91
flutter: AudienceProximityService: Sensor data received - Distance: 0.88m (88cm)
flutter: AudienceProximityService: Calling callback with distance: 0.88m
flutter: CampaignController: Received sensor data: 0.88
flutter: AudienceProximityService: Sensor data received - Distance: 0.85m (85cm)
flutter: AudienceProximityService: Calling callback with distance: 0.85m
flutter: CampaignController: Received sensor data: 0.85
flutter: AudienceProximityService: Sensor data received - Distance: 0.83m (83cm)
flutter: AudienceProximityService: Calling callback with distance: 0.83m
flutter: CampaignController: Received sensor data: 0.83
flutter: AudienceProximityService: Sensor data received - Distance: 0.81m (81cm)
flutter: AudienceProximityService: Calling callback with distance: 0.81m
flutter: CampaignController: Received sensor data: 0.81
flutter: AudienceProximityService: Sensor data received - Distance: 0.84m (84cm)
flutter: AudienceProximityService: Calling callback with distance: 0.84m
flutter: CampaignController: Received sensor data: 0.84
flutter: AudienceProximityService: Sensor data received - Distance: 0.9m (90cm)
flutter: AudienceProximityService: Calling callback with distance: 0.9m
flutter: CampaignController: Received sensor data: 0.9
flutter: AudienceProximityService: Sensor data received - Distance: 0.93m (93cm)
flutter: AudienceProximityService: Calling callback with distance: 0.93m
flutter: CampaignController: Received sensor data: 0.93
flutter: AudienceProximityService: Sensor data received - Distance: 0.94m (94cm)
flutter: AudienceProximityService: Calling callback with distance: 0.94m
flutter: CampaignController: Received sensor data: 0.94
flutter: AudienceProximityService: Sensor data received - Distance: 0.95m (95cm)
flutter: AudienceProximityService: Calling callback with distance: 0.95m
flutter: CampaignController: Received sensor data: 0.95
flutter: AudienceProximityService: Sensor data received - Distance: 0.93m (93cm)
flutter: AudienceProximityService: Calling callback with distance: 0.93m
flutter: CampaignController: Received sensor data: 0.93
flutter: AudienceProximityService: Sensor data received - Distance: 0.94m (94cm)
flutter: AudienceProximityService: Calling callback with distance: 0.94m
flutter: CampaignController: Received sensor data: 0.94
flutter: AudienceProximityService: Sensor data received - Distance: 0.92m (92cm)
flutter: AudienceProximityService: Calling callback with distance: 0.92m
flutter: CampaignController: Received sensor data: 0.92
flutter: AudienceProximityService: Sensor data received - Distance: 0.92m (92cm)
flutter: AudienceProximityService: Calling callback with distance: 0.92m
flutter: CampaignController: Received sensor data: 0.92
flutter: AudienceProximityService: Sensor data received - Distance: 0.94m (94cm)
flutter: AudienceProximityService: Calling callback with distance: 0.94m
flutter: CampaignController: Received sensor data: 0.94
flutter: AudienceProximityService: Sensor data received - Distance: 0.98m (98cm)
flutter: AudienceProximityService: Calling callback with distance: 0.98m
flutter: CampaignController: Received sensor data: 0.98
flutter: AudienceProximityService: Sensor data received - Distance: 0.98m (98cm)
flutter: AudienceProximityService: Calling callback with distance: 0.98m
flutter: CampaignController: Received sensor data: 0.98
flutter: AudienceProximityService: Sensor data received - Distance: 0.97m (97cm)
flutter: AudienceProximityService: Calling callback with distance: 0.97m
flutter: CampaignController: Received sensor data: 0.97
flutter: AudienceProximityService: Sensor data received - Distance: 0.93m (93cm)
flutter: AudienceProximityService: Calling callback with distance: 0.93m
flutter: CampaignController: Received sensor data: 0.93
flutter: AudienceProximityService: Sensor data received - Distance: 0.93m (93cm)
flutter: AudienceProximityService: Calling callback with distance: 0.93m
flutter: CampaignController: Received sensor data: 0.93
flutter: AudienceProximityService: Sensor data received - Distance: 0.93m (93cm)
flutter: AudienceProximityService: Calling callback with distance: 0.93m
flutter: CampaignController: Received sensor data: 0.93
flutter: AudienceProximityService: Sensor data received - Distance: 0.93m (93cm)
flutter: AudienceProximityService: Calling callback with distance: 0.93m
flutter: CampaignController: Received sensor data: 0.93
flutter: AudienceProximityService: Sensor data received - Distance: 0.92m (92cm)
flutter: AudienceProximityService: Calling callback with distance: 0.92m
flutter: CampaignController: Received sensor data: 0.92
flutter: AudienceProximityService: Sensor data received - Distance: 0.91m (91cm)
flutter: AudienceProximityService: Calling callback with distance: 0.91m
flutter: CampaignController: Received sensor data: 0.91
Lost connection to device.