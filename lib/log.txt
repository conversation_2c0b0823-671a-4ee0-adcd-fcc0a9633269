I/flutter ( 4278): Finished all schedule items for campaign Weather Campaign
I/flutter ( 4278): Moving to next campaign...
I/flutter ( 4278): === MOVING TO NEXT VALID CAMPAIGN ===
I/flutter ( 4278): Current campaign index: 1
I/flutter ( 4278): Total campaigns: 3
I/flutter ( 4278): Checking campaign 2: One NZ Promo (trigger_type: 2)
I/flutter ( 4278): PlatformVideoWidget: Android video completed
I/flutter ( 4278): PlaylistWidget: end of playlist, calling onComplete
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Content completed, moving to next item
I/flutter ( 4278): Completed widget details:
I/flutter ( 4278): Name: Weather
I/flutter ( 4278): ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Type: SlideShow
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Finished all schedule items for campaign Weather Campaign
I/flutter ( 4278): Moving to next campaign...
I/flutter ( 4278): === MOVING TO NEXT VALID CAMPAIGN ===
I/flutter ( 4278): Current campaign index: 1
I/flutter ( 4278): Total campaigns: 3
I/flutter ( 4278): Checking campaign 2: One NZ Promo (trigger_type: 2)
I/flutter ( 4278): PlayerControllerService: Timer tick, checking internet connection
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0
I/flutter ( 4278): PlayerControllerService: Updating screen details for screen f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: System health info: {app_version: 0.1.0, build_number: 1, package_name: com.app.signage, memory_total: 7901040, memory_free: 136532, memory_available: 3651228, memory_used: 7764508, memory_usage_percentage: 98.27, internal_storage_total: 111G, internal_storage_used: 67G, internal_storage_available: 44G, internal_storage_usage_percentage: 61, external_storage_total: 111G, external_storage_used: 67G, external_storage_available: 44G, external_storage_usage_percentage: 61, platform: Android, timestamp: 2025-06-01T05:22:12.348804Z}
I/flutter ( 4278): [SupabaseService] Updating screen details for screen f4feb5dc-8546-4ac6-8e07-3ae62cdb6515 to 2025-06-01T05:22:12.349606Z
I/flutter ( 4278): [SupabaseService] Health info: {app_version: 0.1.0, build_number: 1, package_name: com.app.signage, memory_total: 7901040, memory_free: 136532, memory_available: 3651228, memory_used: 7764508, memory_usage_percentage: 98.27, internal_storage_total: 111G, internal_storage_used: 67G, internal_storage_available: 44G, internal_storage_usage_percentage: 61, external_storage_total: 111G, external_storage_used: 67G, external_storage_available: 44G, external_storage_usage_percentage: 61, platform: Android, timestamp: 2025-06-01T05:22:12.348804Z}
I/flutter ( 4278): PlayerControllerService: Received realtime update payload: PostgresChangePayload(schema: public, table: screens, commitTimestamp: 2025-06-01 05:22:13.509Z, eventType: PostgresChangeEvent.update, newRow: {code: 01295, created_at: 2025-04-28T04:30:09.978743+00:00, end_time: 22:00:00, health: {app_version: 0.1.0, build_number: 1, external_storage_available: 44G, external_storage_total: 111G, external_storage_usage_percentage: 61, external_storage_used: 67G, internal_storage_available: 44G, internal_storage_total: 111G, internal_storage_usage_percentage: 61, internal_storage_used: 67G, memory_available: 3651228, memory_free: 136532, memory_total: 7901040, memory_usage_percentage: 98.27, memory_used: 7764508, package_name: com.app.signage, platform: Android, timestamp: 2025-06-01T05:22:12.348804Z}, id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, is_deleted: false, is_registered: true, last_ping_at: 2025-06-01T05:22:12.349606+00:00, location: Main Office Building 1, name: Lobby Screen 1, site_email: , start_time: 08:00:
I/flutter ( 4278): ***************************************************************
I/flutter ( 4278): PlayerControllerService: New row ID: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: Old row ID: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: New update GUID: 6bc84c2d-2c89-49fb-be55-b89ee8337cfe
I/flutter ( 4278): PlayerControllerService: Old update GUID: 6bc84c2d-2c89-49fb-be55-b89ee8337cfe
I/flutter ( 4278): PlayerControllerService: New is_deleted: false
I/flutter ( 4278): ***************************************************************
I/flutter ( 4278): PlayerControllerService: No Updates
I/flutter ( 4278): PlayerControllerService: Successfully updated screen details
I/flutter ( 4278): Device location obtained: lat=23.0121691, lon=72.5576054
I/flutter ( 4278): Geofencing check: campaign=One NZ Promo, device=(23.0121691, 72.5576054), geofence=(23.0055, 72.45), radius=30000.0, inside=true
I/flutter ( 4278): Campaign One NZ Promo meets trigger conditions!
I/flutter ( 4278): Loaded 1 schedule items for campaign One NZ Promo
I/flutter ( 4278):   [0] one-nz-promo.png
I/flutter ( 4278): === MOVED TO CAMPAIGN One NZ Promo ===
I/flutter ( 4278): ====================================================