import 'package:flutter/foundation.dart';

/// Utility class for time-related operations
class TimeUtils {
  /// Parse time string in HH:mm:ss format
  /// Returns null if the format is invalid
  static DateTime? parseTimeString(String timeString) {
    try {
      // Validate format using regex
      final timeRegex = RegExp(r'^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$');
      if (!timeRegex.hasMatch(timeString)) {
        //debugPrint('TimeUtils: Invalid time format: $timeString');
        return null;
      }

      final parts = timeString.split(':');
      if (parts.length != 3) {
        //debugPrint('TimeUtils: Invalid time format: $timeString');
        return null;
      }

      final hour = int.tryParse(parts[0]);
      final minute = int.tryParse(parts[1]);
      final second = int.tryParse(parts[2]);

      if (hour == null || minute == null || second == null) {
        //debugPrint('TimeUtils: Invalid time components: $timeString');
        return null;
      }

      if (hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59) {
        //debugPrint('TimeUtils: Time components out of range: $timeString');
        return null;
      }

      // Create DateTime with today's date and the specified time
      final now = DateTime.now();
      return DateTime(now.year, now.month, now.day, hour, minute, second);
    } catch (e) {
      //debugPrint('TimeUtils: Error parsing time string $timeString: $e');
      return null;
    }
  }

  /// Get current time as DateTime with today's date
  static DateTime getCurrentTime() {
    return DateTime.now();
  }

  /// Calculate duration until target time
  /// If target time is in the past today, calculate for tomorrow
  static Duration calculateDurationUntil(DateTime targetTime) {
    final now = getCurrentTime();

    // If target time is today and in the future
    if (targetTime.isAfter(now)) {
      return targetTime.difference(now);
    }

    // If target time is today but in the past, or we need next day
    // Add 24 hours to get tomorrow's target time
    final tomorrowTarget = targetTime.add(const Duration(days: 1));
    return tomorrowTarget.difference(now);
  }

  /// Calculate duration between current time and target time for today only
  /// Returns negative duration if target time is in the past
  static Duration calculateDurationUntilToday(DateTime targetTime) {
    final now = getCurrentTime();
    return targetTime.difference(now);
  }

  /// Check if current time is between start and end time
  static bool isCurrentTimeBetween(DateTime startTime, DateTime endTime) {
    final now = getCurrentTime();

    // Handle case where end time is on the next day (e.g., start: 22:00, end: 06:00)
    if (endTime.isBefore(startTime)) {
      // End time is next day
      return now.isAfter(startTime) || now.isBefore(endTime);
    } else {
      // Normal case: both times are on the same day
      return now.isAfter(startTime) && now.isBefore(endTime);
    }
  }

  /// Check if current time is before start time
  static bool isCurrentTimeBefore(DateTime startTime) {
    final now = getCurrentTime();
    return now.isBefore(startTime);
  }

  /// Check if current time is after end time
  static bool isCurrentTimeAfter(DateTime endTime) {
    final now = getCurrentTime();
    return now.isAfter(endTime);
  }

  /// Format DateTime to HH:mm:ss string
  static String formatTimeString(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
           '${time.minute.toString().padLeft(2, '0')}:'
           '${time.second.toString().padLeft(2, '0')}';
  }

  /// Get next occurrence of a time (today if future, tomorrow if past)
  static DateTime getNextOccurrence(DateTime targetTime) {
    final now = getCurrentTime();

    if (targetTime.isAfter(now)) {
      return targetTime;
    } else {
      return targetTime.add(const Duration(days: 1));
    }
  }

  /// Calculate duration until next day's target time
  static Duration calculateDurationUntilNextDay(DateTime targetTime) {
    final now = getCurrentTime();
    final nextDayTarget = DateTime(
      now.year,
      now.month,
      now.day + 1,
      targetTime.hour,
      targetTime.minute,
      targetTime.second,
    );
    return nextDayTarget.difference(now);
  }
}
