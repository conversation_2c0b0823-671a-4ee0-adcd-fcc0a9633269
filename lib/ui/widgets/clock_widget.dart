import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';

/// A widget for displaying clock/date content in the SlideShowWidget
class ClockWidget extends StatefulWidget {
  /// The unique ID for this clock widget
  final String id;

  /// The width of the widget
  final double width;

  /// The height of the widget
  final double height;

  /// The text style properties
  final Map<String, dynamic> style;

  /// The animation properties
  final Map<String, dynamic>? animation;

  /// The locale for date/time formatting (e.g., 'en-US', 'en-GB', 'fr-FR')
  final String locale;

  /// The formatting options for date/time
  final Map<String, dynamic> options;

  /// The design width for scaling calculations
  final double? designWidth;

  /// The design height for scaling calculations
  final double? designHeight;

  /// The actual screen width for scaling calculations
  final double? screenWidth;

  /// The actual screen height for scaling calculations
  final double? screenHeight;

  /// Creates a ClockWidget
  const ClockWidget({
    super.key,
    required this.id,
    required this.width,
    required this.height,
    required this.style,
    required this.locale,
    required this.options,
    this.animation,
    this.designWidth,
    this.designHeight,
    this.screenWidth,
    this.screenHeight,
  });

  @override
  State<ClockWidget> createState() => _ClockWidgetState();
}

class _ClockWidgetState extends State<ClockWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isInitialized = false;
  Timer? _timer;
  String _formattedDateTime = '';

  @override
  void initState() {
    super.initState();
    //debugPrint('ClockWidget: initState for ${widget.id}');

    // Initialize locale data for date formatting
    initializeDateFormatting(widget.locale).then((_) {
      //debugPrint('ClockWidget: Locale data initialized for ${widget.locale}');

      if (mounted) {
        // Update the clock after locale initialization
        _updateDateTime();
      }
    });

    // Initialize animation controller
    _initializeAnimation();

    // Initialize the clock
    _updateDateTime();

    // Set up timer to update the clock every second
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateDateTime();
    });
  }

  void _updateDateTime() {
    final now = DateTime.now();
    final formattedDateTime = _formatDateTime(now);

    if (mounted && formattedDateTime != _formattedDateTime) {
      setState(() {
        _formattedDateTime = formattedDateTime;
      });
    }
  }

  String _formatDateTime(DateTime dateTime) {
    try {
      // Get locale from widget
      final String locale = widget.locale;

      // Get formatting options
      final Map<String, dynamic> options = widget.options;

      // Check if we should only show time
      final bool timeOnly = options['timeOnly'] == true;

      //debugPrint('ClockWidget: Formatting date with locale: $locale, options: $options');

      // For specific locales like Spanish, we can use predefined formats
      // or we can build a custom format based on the options
      if (options.isEmpty) {
        // If no specific options are provided, use a locale-specific default format
        if (timeOnly) {
          return DateFormat.jms(locale).format(dateTime);
        } else {
          return DateFormat.yMMMMEEEEd(locale).add_jms().format(dateTime);
        }
      }

      // Create DateFormat pattern based on options
      String pattern = '';

      if (!timeOnly) {
        // Add weekday if specified
        if (options.containsKey('weekday')) {
          final weekdayFormat = options['weekday'] as String;
          switch (weekdayFormat) {
            case 'long':
              pattern += 'EEEE, ';
              break;
            case 'short':
              pattern += 'EEE, ';
              break;
            case 'narrow':
              pattern += 'E, ';
              break;
          }
        }

        // For day-month order, respect the locale's conventions
        // Most non-US locales use day-month order
        bool isDayFirst = !locale.startsWith('en-US');

        // Handle day and month formatting
        String dayPattern = '';
        String monthPattern = '';

        // Format day if specified
        if (options.containsKey('day')) {
          final dayFormat = options['day'] as String;
          dayPattern = dayFormat == '2-digit' ? 'dd' : 'd';
        }

        // Format month if specified
        if (options.containsKey('month')) {
          final monthFormat = options['month'] as String;
          switch (monthFormat) {
            case 'numeric':
              monthPattern = 'M';
              break;
            case '2-digit':
              monthPattern = 'MM';
              break;
            case 'long':
              monthPattern = 'MMMM';
              break;
            case 'short':
              monthPattern = 'MMM';
              break;
            case 'narrow':
              monthPattern = 'MMMMM';
              break;
          }
        }

        // Add day and month in the correct order based on locale
        // For Spanish and most European locales, use 'de' between day and month
        bool useSpanishFormat = locale.startsWith('es') ||
                               locale.startsWith('fr') ||
                               locale.startsWith('it') ||
                               locale.startsWith('pt');

        if (dayPattern.isNotEmpty && monthPattern.isNotEmpty) {
          if (isDayFirst) {
            if (useSpanishFormat && monthPattern.length > 2) {
              // For Spanish-style long format: "17 de mayo de 2023"
              pattern += '$dayPattern \'de\' $monthPattern ';
            } else {
              // For standard European format: "17/05/2023"
              pattern += '$dayPattern/$monthPattern/';
            }
          } else {
            // For US format: "05/17/2023"
            pattern += '$monthPattern/$dayPattern/';
          }
        } else if (dayPattern.isNotEmpty) {
          pattern += '$dayPattern/';
        } else if (monthPattern.isNotEmpty) {
          pattern += '$monthPattern/';
        }

        // Add year if specified
        if (options.containsKey('year')) {
          final yearFormat = options['year'] as String;

          // For Spanish-style long format with 'de' before the year
          if (useSpanishFormat && monthPattern.length > 2) {
            pattern += '\'de\' ';
          }

          pattern += yearFormat == '2-digit' ? 'yy' : 'yyyy';

          // Add a space after the year
          pattern += ' ';
        }
      }

      // Add time components
      if (options.containsKey('hour')) {
        // Determine if 12-hour or 24-hour format
        final bool hour12 = options['hour12'] == true;
        final hourFormat = options['hour'] as String;

        if (hour12) {
          pattern += hourFormat == '2-digit' ? 'hh' : 'h';
        } else {
          pattern += hourFormat == '2-digit' ? 'HH' : 'H';
        }

        // Add minute if specified
        if (options.containsKey('minute')) {
          final minuteFormat = options['minute'] as String;
          pattern += minuteFormat == '2-digit' ? ':mm' : ':m';
        }

        // Add second if specified and not null/empty
        if (options.containsKey('second') && options['second'] != null && options['second'] != '') {
          final secondFormat = options['second'] as String;
          pattern += secondFormat == '2-digit' ? ':ss' : ':s';
        }

        // Add AM/PM for 12-hour format
        if (hour12) {
          // For Spanish locales, we'll handle AM/PM formatting separately
          if (!locale.startsWith('es')) {
            pattern += ' a';  // Standard AM/PM format for non-Spanish locales
          }
        }
      }

      //debugPrint('ClockWidget: Using pattern: "$pattern" with locale: $locale');

      // Create DateFormat with pattern and locale
      final dateFormat = DateFormat(pattern, locale);
      String formattedDate = dateFormat.format(dateTime);

      // Special handling for Spanish AM/PM format
      if (locale.startsWith('es') && options['hour12'] == true) {
        // Get the hour to determine if it's AM or PM
        final hour = dateTime.hour;

        // Replace with proper Spanish AM/PM format
        if (hour < 12) {
          formattedDate += ' a. m.';  // Spanish AM format with dots and spaces
        } else {
          formattedDate += ' p. m.';  // Spanish PM format with dots and spaces
        }
      }

      //debugPrint('ClockWidget: Formatted date: $formattedDate');
      return formattedDate;
    } catch (e) {
      //debugPrint('ClockWidget: Error formatting date/time: $e');
      // Return a simple fallback format with the specified locale
      try {
        return DateFormat.yMd(widget.locale).add_Hms().format(dateTime);
      } catch (_) {
        // If that fails too, use the default locale
        return DateFormat.yMd().add_Hms().format(dateTime);
      }
    }
  }

  void _initializeAnimation() {
    // Default animation duration is 1 second if not specified
    final duration = widget.animation != null
        ? (widget.animation!['duration'] as num?)?.toInt() ?? 1000
        : 1000;

    // Get animation type
    String animationType = 'fade-in';
    if (widget.animation != null && widget.animation!['type'] != null) {
      animationType = widget.animation!['type'] as String;
    }

    // Create animation controller with the specified duration
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: duration),
    );

    // Apply easing if specified
    Curve curve = Curves.linear;
    if (widget.animation != null && widget.animation!['easing'] != null) {
      final easing = widget.animation!['easing'] as String;
      switch (easing) {
        case 'linear':
          curve = Curves.linear;
          break;
        case 'easeIn':
          curve = Curves.easeIn;
          break;
        case 'easeOut':
          curve = Curves.easeOut;
          break;
        case 'easeInOut':
          curve = Curves.easeInOut;
          break;
        case 'circularIn':
          curve = Curves.easeInCirc;
          break;
        case 'circularOut':
          curve = Curves.easeOutCirc;
          break;
        case 'circularInOut':
          curve = Curves.easeInOutCirc;
          break;
        case 'backIn':
          curve = Curves.easeInBack;
          break;
        case 'backOut':
          curve = Curves.easeOutBack;
          break;
        case 'backInOut':
          curve = Curves.easeInOutBack;
          break;
        default:
          curve = Curves.linear;
      }
    }

    // Create the base animation with the curve
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: curve,
    );

    // Apply delay if specified
    if (widget.animation != null && widget.animation!['delay'] != null) {
      final delay = (widget.animation!['delay'] as num?)?.toInt() ?? 0;
      if (delay > 0) {
        Future.delayed(Duration(milliseconds: delay), () {
          if (mounted) {
            _startAnimation(animationType);
          }
        });
      } else {
        _startAnimation(animationType);
      }
    } else {
      _startAnimation(animationType);
    }

    setState(() {
      _isInitialized = true;
    });
  }

  void _startAnimation(String animationType) {
    // For certain animations, we want to ensure they complete a full cycle
    // and end with the text in its original orientation
    if (animationType == 'flip-x' || animationType == 'flip-y' || animationType == 'rotate') {
      _animationController.forward().then((_) {
        // Reset to initial state after animation completes
        if (mounted) {
          _animationController.reset();
        }
      });
    } else if (animationType == 'bounce') {
      // For bounce, we want to ensure it completes and settles
      _animationController.forward();
    } else {
      // For other animations, just run them once
      _animationController.forward();
    }

    // Log the animation type for debugging
    //debugPrint('ClockWidget: Starting animation of type: $animationType');
  }

  @override
  void dispose() {
    //debugPrint('ClockWidget: dispose for ${widget.id}');
    _animationController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.transparent,
      );
    }

    // Parse text style properties
    final textStyle = _parseTextStyle();

    // Parse background color
    final backgroundColor = _parseBackgroundColor();

    // Parse text alignment
    final textAlign = _parseTextAlign();

    // Parse vertical alignment
    final verticalAlign = _parseVerticalAlign();

    // Build the animated widget based on the animation type
    return _buildAnimatedWidget(textStyle, backgroundColor, textAlign, verticalAlign);
  }

  Widget _buildAnimatedWidget(
    TextStyle textStyle,
    Color backgroundColor,
    TextAlign textAlign,
    TextAlignVertical verticalAlign
  ) {
    String animationType = 'fade-in';
    String direction = 'left';

    if (widget.animation != null) {
      if (widget.animation!['type'] != null) {
        animationType = widget.animation!['type'] as String;
      }

      if (widget.animation!['direction'] != null) {
        direction = widget.animation!['direction'] as String;
      }
    }

    final clockContainer = _buildClockContainer(textStyle, backgroundColor, textAlign, verticalAlign);

    switch (animationType) {
      case 'fade-in':
        return FadeTransition(
          opacity: _animation,
          child: clockContainer,
        );

      case 'slide-in':
        // Determine the slide direction
        Offset beginOffset;
        switch (direction) {
          case 'left':
            beginOffset = const Offset(-1.0, 0.0);
            break;
          case 'right':
            beginOffset = const Offset(1.0, 0.0);
            break;
          case 'top':
            beginOffset = const Offset(0.0, -1.0);
            break;
          case 'bottom':
            beginOffset = const Offset(0.0, 1.0);
            break;
          default:
            beginOffset = const Offset(-1.0, 0.0); // Default to left
        }

        // Create slide animation
        final slideAnimation = Tween<Offset>(
          begin: beginOffset,
          end: Offset.zero,
        ).animate(_animationController);

        return SlideTransition(
          position: slideAnimation,
          child: clockContainer,
        );

      case 'zoom-in':
        // For zoom-in, we need a specific Tween for scale
        final scaleAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(_animationController);

        return ScaleTransition(
          scale: scaleAnimation,
          child: clockContainer,
        );

      case 'bounce':
        // For bounce, we use a specialized curve
        final bounceAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.elasticOut,
          ),
        );

        return ScaleTransition(
          scale: bounceAnimation,
          child: clockContainer,
        );

      case 'rotate':
        // For rotate, we animate the rotation around the Z-axis
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.rotate(
              angle: _animationController.value * 2 * 3.14159, // Full 360 degrees
              child: clockContainer,
            );
          },
        );

      case 'flip-x':
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            // For flip-x, we want to create a full 360-degree rotation effect
            // but we need to handle the backface visibility
            final double rotationAngle = _animationController.value * 2 * 3.14159; // Full 360 degrees (2π)

            // Determine if we're showing the front or back of the card
            // When rotation is between 90 and 270 degrees, we're showing the back
            final bool showingBack = rotationAngle > 1.57 && rotationAngle < 4.71;

            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001) // Add perspective
                ..rotateX(rotationAngle),
              // If showing back, apply an additional 180-degree rotation to make text readable
              child: showingBack
                ? Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationX(3.14159), // Rotate 180 degrees
                    child: clockContainer,
                  )
                : clockContainer,
            );
          },
        );

      case 'flip-y':
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            // For flip-y, we want to create a full 360-degree rotation effect
            // but we need to handle the backface visibility
            final double rotationAngle = _animationController.value * 2 * 3.14159; // Full 360 degrees (2π)

            // Determine if we're showing the front or back of the card
            // When rotation is between 90 and 270 degrees, we're showing the back
            final bool showingBack = rotationAngle > 1.57 && rotationAngle < 4.71;

            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001) // Add perspective
                ..rotateY(rotationAngle),
              // If showing back, apply an additional 180-degree rotation to make text readable
              child: showingBack
                ? Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY(3.14159), // Rotate 180 degrees
                    child: clockContainer,
                  )
                : clockContainer,
            );
          },
        );

      default:
        return clockContainer;
    }
  }

  Widget _buildClockContainer(
    TextStyle textStyle,
    Color backgroundColor,
    TextAlign textAlign,
    TextAlignVertical verticalAlign
  ) {
    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(8.0), // Add some padding for better text display
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Center(
        child: FittedBox(
          fit: BoxFit.scaleDown, // Scale down if needed but don't scale up
          alignment: _getAlignment(verticalAlign, textAlign),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: widget.width - 16, // Account for padding
              maxHeight: widget.height - 16,
            ),
            child: Text(
              _formattedDateTime,
              style: textStyle,
              textAlign: textAlign,
              overflow: TextOverflow.visible,
              softWrap: true,
              textScaler: const TextScaler.linear(1.0), // Prevent automatic scaling
            ),
          ),
        ),
      ),
    );
  }

  Alignment _getAlignment(TextAlignVertical verticalAlign, TextAlign textAlign) {
    // Combine vertical and horizontal alignment
    double y;
    switch (verticalAlign) {
      case TextAlignVertical.top:
        y = -1.0;
        break;
      case TextAlignVertical.center:
        y = 0.0;
        break;
      case TextAlignVertical.bottom:
        y = 1.0;
        break;
      default:
        y = 0.0;
    }

    double x;
    switch (textAlign) {
      case TextAlign.left:
        x = -1.0;
        break;
      case TextAlign.center:
        x = 0.0;
        break;
      case TextAlign.right:
        x = 1.0;
        break;
      default:
        x = 0.0;
    }

    return Alignment(x, y);
  }

  TextStyle _parseTextStyle() {
    // Default text style
    TextStyle textStyle = const TextStyle(
      color: Colors.white,
      fontSize: 16,
    );

    if (widget.style.containsKey('style')) {
      final styleMap = widget.style['style'] as Map<String, dynamic>;
      //debugPrint('ClockWidget: Parsing text style: $styleMap');

      // Parse color
      if (styleMap.containsKey('color')) {
        final colorStr = styleMap['color'] as String;
        try {
          textStyle = textStyle.copyWith(color: _parseColor(colorStr));
        } catch (e) {
          //debugPrint('Error parsing text color: $e');
        }
      }

      // Parse font size
      if (styleMap.containsKey('fontSize')) {
        final fontSizeStr = styleMap['fontSize'] as String;
        try {
          final fontSize = double.parse(fontSizeStr.replaceAll('px', ''));
          textStyle = textStyle.copyWith(fontSize: fontSize);
        } catch (e) {
          //debugPrint('Error parsing font size: $e');
        }
      }

      // Parse font weight
      if (styleMap.containsKey('fontWeight')) {
        final fontWeightStr = styleMap['fontWeight'] as String;
        try {
          FontWeight fontWeight = FontWeight.normal;
          if (fontWeightStr == 'bold') {
            fontWeight = FontWeight.bold;
          }
          textStyle = textStyle.copyWith(fontWeight: fontWeight);
        } catch (e) {
          //debugPrint('Error parsing font weight: $e');
        }
      }

      // Parse font style
      if (styleMap.containsKey('fontStyle')) {
        final fontStyleStr = styleMap['fontStyle'] as String;
        try {
          FontStyle fontStyle = FontStyle.normal;
          if (fontStyleStr == 'italic') {
            fontStyle = FontStyle.italic;
          }
          textStyle = textStyle.copyWith(fontStyle: fontStyle);
        } catch (e) {
          //debugPrint('Error parsing font style: $e');
        }
      }

      // Parse font family
      if (styleMap.containsKey('fontFamily')) {
        final fontFamily = styleMap['fontFamily'] as String;
        try {
          textStyle = textStyle.copyWith(fontFamily: fontFamily);
        } catch (e) {
          //debugPrint('Error parsing font family: $e');
        }
      }

      // Parse text decoration
      if (styleMap.containsKey('textDecoration')) {
        final textDecorationStr = styleMap['textDecoration'] as String;
        try {
          TextDecoration textDecoration = TextDecoration.none;
          if (textDecorationStr == 'underline') {
            textDecoration = TextDecoration.underline;
          } else if (textDecorationStr == 'line-through') {
            textDecoration = TextDecoration.lineThrough;
          } else if (textDecorationStr == 'overline') {
            textDecoration = TextDecoration.overline;
          }
          textStyle = textStyle.copyWith(decoration: textDecoration);
        } catch (e) {
          //debugPrint('Error parsing text decoration: $e');
        }
      }
    }

    // Scale the font size based on design dimensions vs actual screen dimensions
    // This ensures the text scales properly with the screen resolution
    // double scaleFactor = 1.0;

    // if (widget.designWidth != null && widget.designHeight != null &&
    //     widget.screenWidth != null && widget.screenHeight != null) {
    //   // Calculate scale factors for both dimensions
    //   final double horizontalScale = widget.screenWidth! / widget.designWidth!;
    //   final double verticalScale = widget.screenHeight! / widget.designHeight!;

    //   // Use the smaller scale factor to ensure text fits properly
    //   scaleFactor = horizontalScale < verticalScale ? horizontalScale : verticalScale;

    //   //debugPrint('ClockWidget: Design dimensions: ${widget.designWidth} x ${widget.designHeight}');
    //   //debugPrint('ClockWidget: Screen dimensions: ${widget.screenWidth} x ${widget.screenHeight}');
    //   //debugPrint('ClockWidget: Scale factors: horizontal=$horizontalScale, vertical=$verticalScale');
    //   //debugPrint('ClockWidget: Using scale factor: $scaleFactor');
    // } else {
    //   // Fallback to the old method if design dimensions are not provided
    //   scaleFactor = widget.width / 500; // Assuming 500 is a reference width
    //   //debugPrint('ClockWidget: Using fallback scaling with factor: $scaleFactor');
    // }

    // final scaledFontSize = textStyle.fontSize! * scaleFactor;
    // textStyle = textStyle.copyWith(fontSize: scaledFontSize);

    // //debugPrint('ClockWidget: Original font size: ${textStyle.fontSize! / scaleFactor}, Scaled font size: $scaledFontSize');
    // //debugPrint('ClockWidget: Final text style: $textStyle');
    return textStyle;
  }

  Color _parseBackgroundColor() {
    Color backgroundColor = Colors.transparent;

    if (widget.style.containsKey('style') &&
        (widget.style['style'] as Map<String, dynamic>).containsKey('backgroundColor')) {
      final colorStr = (widget.style['style'] as Map<String, dynamic>)['backgroundColor'] as String;
      try {
        backgroundColor = _parseColor(colorStr);
      } catch (e) {
        //debugPrint('Error parsing background color: $e');
      }
    }

    return backgroundColor;
  }

  TextAlign _parseTextAlign() {
    TextAlign textAlign = TextAlign.center;

    if (widget.style.containsKey('style') &&
        (widget.style['style'] as Map<String, dynamic>).containsKey('textAlign')) {
      final textAlignStr = (widget.style['style'] as Map<String, dynamic>)['textAlign'] as String;
      switch (textAlignStr) {
        case 'left':
          textAlign = TextAlign.left;
          break;
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'right':
          textAlign = TextAlign.right;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
      }
    }

    return textAlign;
  }

  TextAlignVertical _parseVerticalAlign() {
    TextAlignVertical verticalAlign = TextAlignVertical.center;

    if (widget.style.containsKey('style') &&
        (widget.style['style'] as Map<String, dynamic>).containsKey('verticalAlign')) {
      final verticalAlignStr = (widget.style['style'] as Map<String, dynamic>)['verticalAlign'] as String;
      switch (verticalAlignStr) {
        case 'top':
          verticalAlign = TextAlignVertical.top;
          break;
        case 'middle':
          verticalAlign = TextAlignVertical.center;
          break;
        case 'bottom':
          verticalAlign = TextAlignVertical.bottom;
          break;
      }
    }

    return verticalAlign;
  }

  Color _parseColor(String colorStr) {
    // Handle rgba format
    if (colorStr.startsWith('rgba')) {
      final rgbaValues = colorStr
          .replaceAll('rgba(', '')
          .replaceAll(')', '')
          .split(',')
          .map((s) => s.trim())
          .toList();

      if (rgbaValues.length == 4) {
        final r = int.parse(rgbaValues[0]);
        final g = int.parse(rgbaValues[1]);
        final b = int.parse(rgbaValues[2]);
        final a = double.parse(rgbaValues[3]);

        return Color.fromRGBO(r, g, b, a);
      }
    }

    // Handle hex format
    if (colorStr.startsWith('#')) {
      return Color(int.parse('0xFF${colorStr.substring(1)}'));
    }

    // Default to white if parsing fails
    return Colors.white;
  }
}
