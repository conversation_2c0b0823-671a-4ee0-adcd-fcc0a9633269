import 'dart:math';
import 'package:flutter/material.dart';

/// A widget for displaying various geometric shapes in the SlideShowWidget
class ShapeWidget extends StatefulWidget {
  /// The unique ID for this shape widget
  final String id;

  /// The width of the widget
  final double width;

  /// The height of the widget
  final double height;

  /// The shape properties
  final Map<String, dynamic> content;

  /// The animation properties
  final Map<String, dynamic>? animation;

  /// Creates a ShapeWidget
  const ShapeWidget({
    super.key,
    required this.id,
    required this.width,
    required this.height,
    required this.content,
    this.animation,
  });

  @override
  State<ShapeWidget> createState() => _ShapeWidgetState();
}

class _ShapeWidgetState extends State<ShapeWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    //debugPrint('ShapeWidget: initState for ${widget.id}');

    // Initialize animation controller
    _initializeAnimation();

    setState(() {
      _isInitialized = true;
    });
  }

  void _initializeAnimation() {
    // Default animation duration is 1 second if not specified
    final duration = widget.animation != null
        ? (widget.animation!['duration'] as num?)?.toInt() ?? 1000
        : 1000;

    // Get animation type
    String animationType = 'fade-in';
    if (widget.animation != null && widget.animation!['type'] != null) {
      animationType = widget.animation!['type'] as String;
    }

    // Create animation controller with the specified duration
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: duration),
    );

    // Apply easing if specified
    Curve curve = Curves.linear;
    if (widget.animation != null && widget.animation!['easing'] != null) {
      final easing = widget.animation!['easing'] as String;
      switch (easing) {
        case 'linear':
          curve = Curves.linear;
          break;
        case 'easeIn':
          curve = Curves.easeIn;
          break;
        case 'easeOut':
          curve = Curves.easeOut;
          break;
        case 'easeInOut':
          curve = Curves.easeInOut;
          break;
        case 'circularIn':
        case 'circIn':
          curve = Curves.easeInCirc;
          break;
        case 'circularOut':
        case 'circOut':
          curve = Curves.easeOutCirc;
          break;
        case 'circularInOut':
        case 'circInOut':
          curve = Curves.easeInOutCirc;
          break;
        case 'backIn':
          curve = Curves.easeInBack;
          break;
        case 'backOut':
          curve = Curves.easeOutBack;
          break;
        case 'backInOut':
          curve = Curves.easeInOutBack;
          break;
        default:
          curve = Curves.linear;
      }
    }

    // Create the base animation with the curve
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: curve,
    );

    // Apply delay if specified
    if (widget.animation != null && widget.animation!['delay'] != null) {
      final delay = (widget.animation!['delay'] as num?)?.toInt() ?? 0;
      if (delay > 0) {
        Future.delayed(Duration(milliseconds: delay), () {
          if (mounted) {
            _startAnimation(animationType);
          }
        });
      } else {
        _startAnimation(animationType);
      }
    } else {
      _startAnimation(animationType);
    }
  }

  void _startAnimation(String animationType) {
    // For certain animations, we want to ensure they complete a full cycle
    // and end with the shape in its original orientation
    if (animationType == 'flip-x' || animationType == 'flip-y' || animationType == 'rotate') {
      _animationController.forward().then((_) {
        // Reset to initial state after animation completes
        if (mounted) {
          _animationController.reset();
        }
      });
    } else if (animationType == 'bounce') {
      // For bounce, we want to ensure it completes and settles
      _animationController.forward();
    } else {
      // For other animations, just run them once
      _animationController.forward();
    }

    // Log the animation type for debugging
    //debugPrint('ShapeWidget: Starting animation of type: $animationType');
  }

  @override
  void dispose() {
    //debugPrint('ShapeWidget: dispose for ${widget.id}');
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.transparent,
      );
    }

    // Parse shape properties
    final shapeType = widget.content['shape'] as String? ?? 'rectangle';
    final fillColor = _parseColor(widget.content['fill'] as String? ?? 'rgba(255, 255, 255, 1)');
    final strokeColor = _parseColor(widget.content['stroke'] as String? ?? '#000000');
    final strokeWidth = (widget.content['strokeWidth'] as num?)?.toDouble() ?? 1.0;

    // Build the shape container
    final shapeContainer = _buildShapeContainer(shapeType, fillColor, strokeColor, strokeWidth);

    // Build the animated widget based on the animation type
    return _buildAnimatedWidget(shapeContainer);
  }

  Widget _buildAnimatedWidget(Widget shapeContainer) {
    String animationType = 'fade-in';
    String direction = 'left';

    if (widget.animation != null) {
      if (widget.animation!['type'] != null) {
        animationType = widget.animation!['type'] as String;
      }

      if (widget.animation!['direction'] != null) {
        direction = widget.animation!['direction'] as String;
      }
    }

    switch (animationType) {
      case 'fade-in':
        return FadeTransition(
          opacity: _animation,
          child: shapeContainer,
        );

      case 'slide-in':
        // Determine the slide direction
        Offset beginOffset;
        switch (direction) {
          case 'left':
            beginOffset = const Offset(-1.0, 0.0);
            break;
          case 'right':
            beginOffset = const Offset(1.0, 0.0);
            break;
          case 'top':
            beginOffset = const Offset(0.0, -1.0);
            break;
          case 'bottom':
            beginOffset = const Offset(0.0, 1.0);
            break;
          default:
            beginOffset = const Offset(-1.0, 0.0); // Default to left
        }

        // Create slide animation
        final slideAnimation = Tween<Offset>(
          begin: beginOffset,
          end: Offset.zero,
        ).animate(_animationController);

        return SlideTransition(
          position: slideAnimation,
          child: shapeContainer,
        );

      case 'zoom-in':
        // For zoom-in, we need a specific Tween for scale
        final scaleAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(_animationController);

        return ScaleTransition(
          scale: scaleAnimation,
          child: shapeContainer,
        );

      case 'bounce':
        // For bounce, we use a specialized curve
        final bounceAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.elasticOut,
          ),
        );

        return ScaleTransition(
          scale: bounceAnimation,
          child: shapeContainer,
        );

      case 'rotate':
        // For rotate, we animate the rotation around the Z-axis
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.rotate(
              angle: _animationController.value * 2 * pi, // Full 360 degrees
              child: shapeContainer,
            );
          },
        );

      case 'flip-x':
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            // For flip-x, we want to create a full 360-degree rotation effect
            // but we need to handle the backface visibility
            final double rotationAngle = _animationController.value * 2 * pi; // Full 360 degrees (2π)

            // Determine if we're showing the front or back of the card
            // When rotation is between 90 and 270 degrees, we're showing the back
            final bool showingBack = rotationAngle > 1.57 && rotationAngle < 4.71;

            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001) // Add perspective
                ..rotateX(rotationAngle),
              // If showing back, apply an additional 180-degree rotation to make shape readable
              child: showingBack
                ? Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationX(pi), // Rotate 180 degrees
                    child: shapeContainer,
                  )
                : shapeContainer,
            );
          },
        );

      case 'flip-y':
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            // For flip-y, we want to create a full 360-degree rotation effect
            // but we need to handle the backface visibility
            final double rotationAngle = _animationController.value * 2 * pi; // Full 360 degrees (2π)

            // Determine if we're showing the front or back of the card
            // When rotation is between 90 and 270 degrees, we're showing the back
            final bool showingBack = rotationAngle > 1.57 && rotationAngle < 4.71;

            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001) // Add perspective
                ..rotateY(rotationAngle),
              // If showing back, apply an additional 180-degree rotation to make shape readable
              child: showingBack
                ? Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY(pi), // Rotate 180 degrees
                    child: shapeContainer,
                  )
                : shapeContainer,
            );
          },
        );

      default:
        return shapeContainer;
    }
  }

  /// Parse color from string format (rgba, rgb, or hex)
  Color _parseColor(String colorStr) {
    try {
      if (colorStr.startsWith('rgba')) {
        // Parse rgba format: rgba(255, 0, 0, 0.5)
        final rgbaValues = colorStr
            .replaceAll('rgba(', '')
            .replaceAll(')', '')
            .split(',')
            .map((s) => s.trim())
            .toList();

        return Color.fromRGBO(
          int.parse(rgbaValues[0]),
          int.parse(rgbaValues[1]),
          int.parse(rgbaValues[2]),
          double.parse(rgbaValues[3]),
        );
      } else if (colorStr.startsWith('rgb')) {
        // Parse rgb format: rgb(255, 0, 0)
        final rgbValues = colorStr
            .replaceAll('rgb(', '')
            .replaceAll(')', '')
            .split(',')
            .map((s) => s.trim())
            .toList();

        return Color.fromRGBO(
          int.parse(rgbValues[0]),
          int.parse(rgbValues[1]),
          int.parse(rgbValues[2]),
          1.0,
        );
      } else {
        // Parse hex format: #FF0000 or #FF0000FF
        String hexColor = colorStr.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor'; // Add alpha if not provided
        }
        return Color(int.parse(hexColor, radix: 16));
      }
    } catch (e) {
      //debugPrint('Error parsing color: $e');
      return Colors.black; // Default to black if parsing fails
    }
  }

  Widget _buildShapeContainer(
    String shapeType,
    Color fillColor,
    Color strokeColor,
    double strokeWidth
  ) {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.transparent,
      child: CustomPaint(
        size: Size(widget.width, widget.height),
        painter: ShapePainter(
          shapeType: shapeType,
          fillColor: fillColor,
          strokeColor: strokeColor,
          strokeWidth: strokeWidth,
        ),
      ),
    );
  }
}

/// Custom painter for drawing various shapes
class ShapePainter extends CustomPainter {
  /// The type of shape to draw
  final String shapeType;

  /// The fill color of the shape
  final Color fillColor;

  /// The stroke color of the shape
  final Color strokeColor;

  /// The stroke width of the shape
  final double strokeWidth;

  /// Creates a ShapePainter
  ShapePainter({
    required this.shapeType,
    required this.fillColor,
    required this.strokeColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = strokeColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    // Calculate center and radius based on the smaller dimension
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2 - strokeWidth;

    switch (shapeType) {
      case 'rectangle':
        _drawRectangle(canvas, size, paint, strokePaint);
        break;
      case 'circle':
        _drawCircle(canvas, center, radius, paint, strokePaint);
        break;
      case 'triangle':
        _drawTriangle(canvas, size, paint, strokePaint);
        break;
      case 'pentagon':
        _drawRegularPolygon(canvas, center, radius, 5, paint, strokePaint);
        break;
      case 'hexagon':
        _drawRegularPolygon(canvas, center, radius, 6, paint, strokePaint);
        break;
      case 'octagon':
        _drawRegularPolygon(canvas, center, radius, 8, paint, strokePaint);
        break;
      case 'star':
        _drawStar(canvas, center, radius, paint, strokePaint);
        break;
      case 'heart':
        _drawHeart(canvas, center, radius, paint, strokePaint);
        break;
      case 'oval':
        _drawOval(canvas, size, paint, strokePaint);
        break;
      case 'semicircle':
        _drawSemicircle(canvas, size, paint, strokePaint);
        break;
      default:
        // Default to rectangle if shape type is not recognized
        _drawRectangle(canvas, size, paint, strokePaint);
    }
  }

  void _drawRectangle(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    final rect = Rect.fromLTWH(
      strokePaint.strokeWidth / 2,
      strokePaint.strokeWidth / 2,
      size.width - strokePaint.strokeWidth,
      size.height - strokePaint.strokeWidth,
    );

    canvas.drawRect(rect, fillPaint);
    canvas.drawRect(rect, strokePaint);
  }

  void _drawCircle(Canvas canvas, Offset center, double radius, Paint fillPaint, Paint strokePaint) {
    canvas.drawCircle(center, radius, fillPaint);
    canvas.drawCircle(center, radius, strokePaint);
  }

  void _drawTriangle(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    final path = Path();

    // Start at top center
    path.moveTo(size.width / 2, strokePaint.strokeWidth);

    // Draw to bottom right
    path.lineTo(size.width - strokePaint.strokeWidth, size.height - strokePaint.strokeWidth);

    // Draw to bottom left
    path.lineTo(strokePaint.strokeWidth, size.height - strokePaint.strokeWidth);

    // Close the path
    path.close();

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, strokePaint);
  }

  void _drawRegularPolygon(Canvas canvas, Offset center, double radius, int sides, Paint fillPaint, Paint strokePaint) {
    final path = Path();
    final angle = (2 * pi) / sides;

    // Start at the rightmost point
    path.moveTo(
      center.dx + radius * cos(0),
      center.dy + radius * sin(0),
    );

    // Draw the polygon
    for (int i = 1; i < sides; i++) {
      final x = center.dx + radius * cos(angle * i);
      final y = center.dy + radius * sin(angle * i);
      path.lineTo(x, y);
    }

    // Close the path
    path.close();

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, strokePaint);
  }

  void _drawStar(Canvas canvas, Offset center, double radius, Paint fillPaint, Paint strokePaint) {
    final path = Path();
    final outerRadius = radius;
    final innerRadius = radius * 0.5; // Inner radius for the star points
    final points = 5; // 5-pointed star

    final angleIncrement = (2 * pi) / (points * 2);

    // Start at the top point
    path.moveTo(
      center.dx,
      center.dy - outerRadius,
    );

    // Draw the star
    for (int i = 1; i < points * 2; i++) {
      final currentRadius = i.isOdd ? innerRadius : outerRadius;
      final x = center.dx + currentRadius * sin(angleIncrement * i);
      final y = center.dy - currentRadius * cos(angleIncrement * i);
      path.lineTo(x, y);
    }

    // Close the path
    path.close();

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, strokePaint);
  }

  void _drawHeart(Canvas canvas, Offset center, double radius, Paint fillPaint, Paint strokePaint) {
    final path = Path();
    final width = radius * 2;
    final height = radius * 2;

    // Start at the bottom point
    path.moveTo(center.dx, center.dy + height / 2);

    // Draw the left curve
    path.cubicTo(
      center.dx - width / 2, center.dy, // Control point 1
      center.dx - width / 2, center.dy - height / 3, // Control point 2
      center.dx, center.dy - height / 6, // End point
    );

    // Draw the right curve
    path.cubicTo(
      center.dx + width / 2, center.dy - height / 3, // Control point 1
      center.dx + width / 2, center.dy, // Control point 2
      center.dx, center.dy + height / 2, // End point
    );

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, strokePaint);
  }

  void _drawOval(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    final rect = Rect.fromLTWH(
      strokePaint.strokeWidth / 2,
      strokePaint.strokeWidth / 2,
      size.width - strokePaint.strokeWidth,
      size.height - strokePaint.strokeWidth,
    );

    canvas.drawOval(rect, fillPaint);
    canvas.drawOval(rect, strokePaint);
  }

  void _drawSemicircle(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    final rect = Rect.fromLTWH(
      strokePaint.strokeWidth / 2,
      strokePaint.strokeWidth / 2,
      size.width - strokePaint.strokeWidth,
      (size.height - strokePaint.strokeWidth) * 2,
    );

    final path = Path();
    path.addArc(rect, pi, pi);

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, strokePaint);
  }

  @override
  bool shouldRepaint(ShapePainter oldDelegate) {
    return oldDelegate.shapeType != shapeType ||
           oldDelegate.fillColor != fillColor ||
           oldDelegate.strokeColor != strokeColor ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}