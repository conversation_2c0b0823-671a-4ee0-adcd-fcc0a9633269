import 'dart:io';
import 'package:flutter/material.dart';
import 'package:signage/utils/platform_utils.dart';

// Platform-specific imports
import 'package:video_player/video_player.dart' as vp;
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';

/// A platform-specific video widget that uses media_kit for desktop and video_player for Android
class PlatformVideoWidget extends StatefulWidget {
  /// The path to the video file (for local files)
  final String? filePath;

  /// The URL for network videos
  final String? networkUrl;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Whether to loop the video
  final bool loop;

  /// Callback when the video is initialized
  final VoidCallback? onInitialized;

  /// Callback when the video playback ends
  final VoidCallback? onEnded;

  /// Callback when there's an error playing the video
  final Function(Object)? onError;

  /// Creates a PlatformVideoWidget
  const PlatformVideoWidget({
    super.key,
    this.filePath,
    this.networkUrl,
    this.width,
    this.height,
    this.loop = false,
    this.onInitialized,
    this.onEnded,
    this.onError,
  }) : assert(filePath != null || networkUrl != null, 'Either filePath or networkUrl must be provided');

  @override
  State<PlatformVideoWidget> createState() => _PlatformVideoWidgetState();
}

class _PlatformVideoWidgetState extends State<PlatformVideoWidget> {
  // Android video player controller
  vp.VideoPlayerController? _androidController;

  // Desktop media kit player and controller
  Player? _desktopPlayer;
  VideoController? _desktopController;

  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _hasCalledOnEnded = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
  }

  @override
  void didUpdateWidget(PlatformVideoWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the file path or network URL changed, reinitialize the player
    if (oldWidget.filePath != widget.filePath || oldWidget.networkUrl != widget.networkUrl) {
      _disposeCurrentPlayer();
      _hasCalledOnEnded = false; // Reset the flag
      _initializeVideoPlayer();
    }
  }

  Future<void> _initializeVideoPlayer() async {
    try {
      if (PlatformUtils.isAndroid) {
        await _initializeAndroidPlayer();
      } else if (PlatformUtils.isDesktop) {
        await _initializeDesktopPlayer();
      } else {
        throw UnsupportedError('Platform not supported for video playback');
      }
    } catch (e) {
      //debugPrint('PlatformVideoWidget: Error initializing video player: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = e.toString();
        });
        if (widget.onError != null) {
          widget.onError!(e);
        }
      }
    }
  }

  Future<void> _initializeAndroidPlayer() async {
    // Create a VideoPlayerController for Android
    if (widget.filePath != null) {
      _androidController = vp.VideoPlayerController.file(File(widget.filePath!));
    } else if (widget.networkUrl != null) {
      _androidController = vp.VideoPlayerController.networkUrl(Uri.parse(widget.networkUrl!));
    }

    if (_androidController == null) return;

    // Initialize the controller
    await _androidController!.initialize();

    if (!mounted) return;

    // Set looping based on the widget parameter
    await _androidController!.setLooping(widget.loop);

    if (!mounted) return;

    // Add a listener for video completion
    _androidController!.addListener(_androidVideoListener);

    // Start playing the video
    await _androidController!.play();

    if (!mounted) return;

    // Update the state
    setState(() {
      _isInitialized = true;
    });

    // Call the initialized callback if provided
    if (widget.onInitialized != null) {
      widget.onInitialized!();
    }
  }

  Future<void> _initializeDesktopPlayer() async {
    // Create media kit player for desktop
    _desktopPlayer = Player();
    _desktopController = VideoController(_desktopPlayer!);

    // Set up event listeners
    _desktopPlayer!.stream.completed.listen((completed) {
      if (completed && !_hasCalledOnEnded) {
        //debugPrint('PlatformVideoWidget: Desktop video completed');
        _hasCalledOnEnded = true;
        if (widget.onEnded != null) {
          widget.onEnded!();
        }
      }
    });

    _desktopPlayer!.stream.error.listen((error) {
      //debugPrint('PlatformVideoWidget: Desktop video error: $error');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = error.toString();
        });
        if (widget.onError != null) {
          widget.onError!(error);
        }
      }
    });

    // Open the media
    String mediaSource;
    if (widget.filePath != null) {
      mediaSource = widget.filePath!;
    } else {
      mediaSource = widget.networkUrl!;
    }

    await _desktopPlayer!.open(Media(mediaSource));

    if (!mounted) return;

    // Set looping
    await _desktopPlayer!.setPlaylistMode(widget.loop ? PlaylistMode.single : PlaylistMode.none);

    if (!mounted) return;

    // Update the state
    setState(() {
      _isInitialized = true;
    });

    // Call the initialized callback if provided
    if (widget.onInitialized != null) {
      widget.onInitialized!();
    }
  }

  void _androidVideoListener() {
    if (_androidController?.value.isInitialized == true) {
      final position = _androidController!.value.position;
      final duration = _androidController!.value.duration;

      // Check if video has ended (within 100ms of duration to account for timing issues)
      if (position.inMilliseconds >= (duration.inMilliseconds - 100) &&
          !_androidController!.value.isPlaying &&
          !_hasCalledOnEnded) {
        //debugPrint('PlatformVideoWidget: Android video completed');
        _hasCalledOnEnded = true;
        if (widget.onEnded != null) {
          widget.onEnded!();
        }
      }
    }
  }

  void _disposeCurrentPlayer() {
    if (PlatformUtils.isAndroid && _androidController != null) {
      _androidController!.removeListener(_androidVideoListener);
      _androidController!.dispose();
      _androidController = null;
    } else if (PlatformUtils.isDesktop) {
      _desktopPlayer?.dispose();
      // VideoController doesn't have a dispose method in media_kit
      _desktopPlayer = null;
      _desktopController = null;
    }
  }

  @override
  void dispose() {
    //debugPrint('PlatformVideoWidget: Disposing video player');
    _disposeCurrentPlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show error state
    if (_hasError) {
      return SizedBox(
        width: widget.width,
        height: widget.height,
        child: Container(
          color: Colors.black,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  'Video Error',
                  style: const TextStyle(color: Colors.white, fontSize: 18),
                ),
                const SizedBox(height: 8),
                Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Show loading state
    if (!_isInitialized) {
      return SizedBox(
        width: widget.width,
        height: widget.height,
        child: Container(
          color: Colors.transparent,
          child: null, // No loading indicator for any platform
        ),
      );
    }

    // Show the video based on platform
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Background
          Container(
            color: Colors.black,
          ),
          // Platform-specific video player
          Positioned.fill(
            child: _buildPlatformSpecificPlayer(),
          ),
        ],
      ),
    );
  }

  Widget _buildPlatformSpecificPlayer() {
    if (PlatformUtils.isAndroid && _androidController != null) {
      return FittedBox(
        fit: BoxFit.fill, // Force stretching without preserving aspect ratio
        clipBehavior: Clip.hardEdge,
        child: SizedBox(
          width: _androidController!.value.size.width,
          height: _androidController!.value.size.height,
          child: vp.VideoPlayer(_androidController!),
        ),
      );
    } else if (PlatformUtils.isDesktop && _desktopController != null) {
      return MouseRegion(
        cursor: SystemMouseCursors.none, // Always hide cursor on video element
        child: Video(
          controller: _desktopController!,
          fit: BoxFit.fill, // Force stretching without preserving aspect ratio
          controls: null, // Hide video controls (NoVideoControls)
        ),
      );
    } else {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            'Unsupported platform',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }
  }
}
