import 'dart:async';
import 'package:flutter/material.dart';
import 'package:signage/core/models/schedule_item.dart';
import 'package:signage/core/services/campaign_controller.dart';
import 'package:signage/ui/widgets/image_timer_widget.dart';
import 'package:signage/ui/widgets/video_widget.dart';

/// A widget for displaying playlist content
class PlaylistWidget extends StatefulWidget {
  /// The ID of the playlist
  final String id;

  /// The playlist items to display
  final List<PlaylistItem> playlistItems;

  /// The campaign controller
  final CampaignController campaignController;

  /// The width of the widget
  final double width;

  /// The height of the widget
  final double height;

  /// Callback when all playlist items have completed
  final VoidCallback onComplete;

  /// Creates a PlaylistWidget
  const PlaylistWidget({
    super.key,
    required this.id,
    required this.playlistItems,
    required this.campaignController,
    required this.width,
    required this.height,
    required this.onComplete,
  });

  @override
  State<PlaylistWidget> createState() => _PlaylistWidgetState();
}

class _PlaylistWidgetState extends State<PlaylistWidget> {
  int _currentIndex = 0;
  Widget? _currentItemWidget;
  Widget? _nextItemWidget;
  bool _isInitialized = false;

  // Transition state
  bool _isTransitioning = false;
  double _currentOpacity = 1.0;
  double _nextOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    //debugPrint('PlaylistWidget: initState for ${widget.id}');

    // Initialize the playlist
    _initializePlaylist();
  }

  Future<void> _initializePlaylist() async {
    if (widget.playlistItems.isEmpty) {
      //debugPrint('PlaylistWidget: no playlist items, completing immediately');
      widget.onComplete();
      return;
    }

    // Load the first item
    await _loadPlaylistItem(0);

    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  Future<void> _loadPlaylistItem(int index) async {
    if (index >= widget.playlistItems.length) {
      //debugPrint('PlaylistWidget: index out of bounds, completing playlist');
      widget.onComplete();
      return;
    }

    final item = widget.playlistItems[index];
    //debugPrint('PlaylistWidget: loading item ${item.name} at index $index');

    // Get the file path
    final filePath = await widget.campaignController.getMediaFilePath(item.name);
    if (filePath == null) {
      //debugPrint('PlaylistWidget: failed to get file path for ${item.name}');
      _nextItem();
      return;
    }

    if (!mounted) return;

    // Create the appropriate widget based on the file type
    Widget itemWidget;

    // Check if it's an image or video
    if (widget.campaignController.isImageFile(item.name)) {
      // Create an image timer widget - no need for Positioned as we'll fill the entire space
      itemWidget = ImageTimerWidget(
        key: ValueKey('playlist-image-${item.id}'),
        filePath: filePath,
        width: widget.width,
        height: widget.height,
        durationInSeconds: item.duration > 0 ? item.duration : 5,
        onComplete: _nextItem,
      );
    } else if (widget.campaignController.isVideoFile(item.name)) {
      // Create a video widget - no need for Positioned as we'll fill the entire space
      itemWidget = VideoWidget(
        key: ValueKey('playlist-video-${item.id}'),
        filePath: filePath,
        width: widget.width,
        height: widget.height,
        loop: false,
        onEnded: _nextItem,
        onError: (error) {
          //debugPrint('PlaylistWidget: error playing video: $error');
          _nextItem();
        },
      );
    } else {
      // Unknown file type, skip to next item
      //debugPrint('PlaylistWidget: unknown file type for ${item.name}');
      _nextItem();
      return;
    }

    if (mounted) {
      // If we're already showing content, start a transition
      if (_currentItemWidget != null) {
        setState(() {
          _nextItemWidget = itemWidget;
          _currentIndex = index;
          _isTransitioning = true;

          // Start the transition animation
          _startTransitionAnimation();
        });
      } else {
        // First item, just show it directly
        setState(() {
          _currentItemWidget = itemWidget;
          _currentIndex = index;
        });
      }
    }
  }

  /// Start the transition animation between current and next content
  void _startTransitionAnimation() {
    //debugPrint('PlaylistWidget: Starting transition animation');

    // Reset opacity values
    _currentOpacity = 1.0;
    _nextOpacity = 0.0;

    // Use an animation to fade between widgets
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!mounted) return;

      // Start fading in the next content
      setState(() {
        _nextOpacity = 0.3;
      });

      Future.delayed(const Duration(milliseconds: 100), () {
        if (!mounted) return;

        setState(() {
          _nextOpacity = 0.6;
          _currentOpacity = 0.7;
        });

        Future.delayed(const Duration(milliseconds: 100), () {
          if (!mounted) return;

          setState(() {
            _nextOpacity = 1.0;
            _currentOpacity = 0.3;
          });

          // Complete the transition after a short delay
          Future.delayed(const Duration(milliseconds: 100), () {
            if (!mounted) return;

            _completeTransition();
          });
        });
      });
    });
  }

  /// Complete the transition by making the next content the current content
  void _completeTransition() {
    //debugPrint('PlaylistWidget: Completing transition');

    if (!mounted) return;

    // Store the previous widget for disposal
    final previousWidget = _currentItemWidget;

    // Update the current content with the next content
    setState(() {
      // Move next content to current
      _currentItemWidget = _nextItemWidget;

      // Reset next content
      _nextItemWidget = null;

      // Reset opacity
      _currentOpacity = 1.0;
      _nextOpacity = 0.0;

      // End transition
      _isTransitioning = false;
    });

    // Dispose the previous widget after a short delay
    // This ensures the new content is fully visible before disposing the old one
    if (previousWidget != null) {
      Future.delayed(const Duration(milliseconds: 500), () {
        //debugPrint('PlaylistWidget: Disposing previous widget after transition');
        // The widget will be automatically disposed by Flutter
      });
    }
  }

  void _nextItem() {
    if (!mounted) return;

    final nextIndex = _currentIndex + 1;

    if (nextIndex >= widget.playlistItems.length) {
      // End of playlist, call the completion callback
      //debugPrint('PlaylistWidget: end of playlist, calling onComplete');
      widget.onComplete();
    } else {
      // Load the next item with transition
      _loadPlaylistItem(nextIndex);
    }
  }

  @override
  void dispose() {
    //debugPrint('PlaylistWidget: dispose for ${widget.id}');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _currentItemWidget == null) {
      // Return a black container while initializing
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.black,
      );
    }

    //debugPrint('PlaylistWidget: Building with dimensions ${widget.width} x ${widget.height}');

    // Return the current item widget with a SizedBox to ensure it fills the available space
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Stack(
        key: ValueKey('playlist-${widget.id}'),
        fit: StackFit.expand, // Ensure stack fills the available space
        children: [
          // Black background
          Container(
            color: Colors.black,
          ),

          // During transition, show both widgets with opacity
          if (_isTransitioning && _currentItemWidget != null)
            Positioned.fill(
              child: Opacity(
                opacity: _currentOpacity,
                child: FittedBox(
                  fit: BoxFit.fill, // Force stretching without preserving aspect ratio
                  clipBehavior: Clip.hardEdge,
                  child: SizedBox(
                    width: widget.width,
                    height: widget.height,
                    child: _currentItemWidget!,
                  ),
                ),
              ),
            ),

          // Next item during transition or current item during normal playback
          Positioned.fill(
            child: Opacity(
              opacity: _isTransitioning ? _nextOpacity : 1.0,
              child: FittedBox(
                fit: BoxFit.fill, // Force stretching without preserving aspect ratio
                clipBehavior: Clip.hardEdge,
                child: SizedBox(
                  width: widget.width,
                  height: widget.height,
                  child: _isTransitioning ? _nextItemWidget! : _currentItemWidget!,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
