import 'dart:io';
import 'package:flutter/material.dart';
import 'package:signage/ui/widgets/image_timer_widget.dart';
import 'package:signage/ui/widgets/video_widget.dart';

/// A widget for displaying simple media content (images and videos)
class SimpleMediaWidget extends StatefulWidget {
  /// The ID of the schedule item
  final String id;
  
  /// The name of the media file
  final String name;
  
  /// The path to the media file
  final String filePath;
  
  /// The width of the widget
  final double width;
  
  /// The height of the widget
  final double height;
  
  /// Whether the media is an image
  final bool isImage;
  
  /// Whether the media is a video
  final bool isVideo;
  
  /// Callback when the content playback completes
  final VoidCallback onComplete;

  /// Creates a SimpleMediaWidget
  const SimpleMediaWidget({
    super.key,
    required this.id,
    required this.name,
    required this.filePath,
    required this.width,
    required this.height,
    required this.isImage,
    required this.isVideo,
    required this.onComplete,
  });

  @override
  State<SimpleMediaWidget> createState() => _SimpleMediaWidgetState();
}

class _SimpleMediaWidgetState extends State<SimpleMediaWidget> {
  @override
  void initState() {
    super.initState();
    //debugPrint('SimpleMediaWidget: initState for ${widget.name}');
  }

  @override
  void dispose() {
    //debugPrint('SimpleMediaWidget: dispose for ${widget.name}');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Check if the file exists
    final file = File(widget.filePath);
    if (!file.existsSync()) {
      //debugPrint('SimpleMediaWidget: file does not exist: ${widget.filePath}');
      // Call the completion callback immediately if the file doesn't exist
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onComplete();
      });
      
      // Return a black container
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.black,
      );
    }

    // Create the appropriate widget based on the media type
    if (widget.isImage) {
      return _buildImageWidget();
    } else if (widget.isVideo) {
      return _buildVideoWidget();
    } else {
      // Unknown media type, return a black container
      //debugPrint('SimpleMediaWidget: unknown media type for ${widget.name}');
      
      // Call the completion callback immediately
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onComplete();
      });
      
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.black,
      );
    }
  }

  /// Build an image widget
  Widget _buildImageWidget() {
    return Stack(
      key: ValueKey('simple-image-${widget.id}'),
      children: [
        // Black background
        Container(
          width: widget.width,
          height: widget.height,
          color: Colors.black,
        ),
        
        // Image with timer
        ImageTimerWidget(
          key: ValueKey('image-${widget.id}'),
          filePath: widget.filePath,
          width: widget.width,
          height: widget.height,
          durationInSeconds: 8, // Standard 8 seconds for images
          onComplete: widget.onComplete,
        ),
      ],
    );
  }

  /// Build a video widget
  Widget _buildVideoWidget() {
    return Stack(
      key: ValueKey('simple-video-${widget.id}'),
      children: [
        // Black background
        Container(
          width: widget.width,
          height: widget.height,
          color: Colors.black,
        ),
        
        // Video
        VideoWidget(
          key: ValueKey('video-${widget.id}'),
          filePath: widget.filePath,
          width: widget.width,
          height: widget.height,
          loop: false,
          onEnded: widget.onComplete,
          onError: (error) {
            //debugPrint('SimpleMediaWidget: error playing video: $error');
            widget.onComplete();
          },
        ),
      ],
    );
  }
}
