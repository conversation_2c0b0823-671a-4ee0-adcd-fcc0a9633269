import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:signage/core/models/schedule_item.dart';
import 'package:signage/core/services/campaign_controller.dart';
import 'package:signage/core/services/playlist_manager.dart';
import 'package:signage/ui/widgets/image_timer_widget.dart';
import 'package:signage/ui/widgets/video_widget.dart';

/// A widget for displaying slide campaigns
class SlideWidget extends StatefulWidget {
  /// The schedule item containing slide data
  final ScheduleItem scheduleItem;

  /// The campaign controller
  final CampaignController campaignController;

  /// Callback when all content in the slide has finished playing
  final VoidCallback onComplete;

  /// Creates a SlideWidget
  const SlideWidget({
    super.key,
    required this.scheduleItem,
    required this.campaignController,
    required this.onComplete,
  });

  /// Check if the widget is fully initialized and ready to display
  bool isFullyInitialized() {
    // Forward to the state
    final state = _SlideWidgetState.of(this);
    return state?.isFullyInitialized() ?? false;
  }

  @override
  State<SlideWidget> createState() => _SlideWidgetState();
}

class _SlideWidgetState extends State<SlideWidget> {
  // Static map to store state instances by widget key
  static final Map<Key?, _SlideWidgetState> _states = {};

  // Static method to get the state for a widget
  static _SlideWidgetState? of(SlideWidget widget) {
    return _states[widget.key];
  }

  List<dynamic> _slideContent = [];
  final Map<String, PlaylistManager> _playlistManagers = {};
  final Map<String, Widget> _contentWidgets = {};
  int _completedPlaylists = 0;
  int _totalPlaylists = 0;

  // Cache for media file paths to avoid redundant async operations
  final Map<String, Future<String?>> _mediaPathCache = {};

  // Flag to track if the widget is fully initialized
  bool _isFullyInitialized = false;

  @override
  void initState() {
    super.initState();
    //debugPrint('SlideWidget initState for ${widget.scheduleItem.name}');

    // Register this state instance
    if (widget.key != null) {
      _states[widget.key] = this;
    }

    try {
      // Parse content immediately instead of in a microtask
      // This ensures the content is parsed before the widget is built
      _parseSlideContent();

      // Set up playlist listeners
      _setupPlaylistListeners();

      // Check if we have playlists to display
      if (_totalPlaylists == 0) {
        //debugPrint('No playlists found in slide, calling onComplete immediately');
        widget.onComplete();
      } else {
        //debugPrint('SlideWidget initialized with $_totalPlaylists playlists');

        // Mark the widget as fully initialized
        _isFullyInitialized = true;
        //debugPrint('SlideWidget is fully initialized: ${widget.scheduleItem.name}');
      }
    } catch (e) {
      //debugPrint('Error in SlideWidget initState: $e');
      // Call onComplete immediately on error
      if (mounted) {
        //debugPrint('Calling onComplete due to error: $e');
        widget.onComplete();
      }
    }
  }

  /// Set up listeners for playlist managers
  void _setupPlaylistListeners() {
    for (final entry in _playlistManagers.entries) {
      final id = entry.key;
      final manager = entry.value;

      //debugPrint('Setting up listener for playlist $id');

      // Add a listener to rebuild when playlist managers change
      manager.addListener(() {
        if (mounted) {
          // Get the current index before updating state
          final currentIndex = manager.currentIndex;
          final widgetKey = '$id-$currentIndex';

          // Only rebuild if we don't already have this widget cached
          // This prevents unnecessary rebuilds when the same item is shown multiple times
          if (!_contentWidgets.containsKey(widgetKey)) {
            //debugPrint('PlaylistManager $id changed to index $currentIndex, rebuilding');

            // Update state directly without microtask delay
            setState(() {
              // We don't need to remove anything here since we're only adding a new widget
              // The cleanup happens in _buildSlideContent
            });
          } else {
            //debugPrint('PlaylistManager $id changed to index $currentIndex, widget already cached');
          }
        }
      });
    }
  }

  @override
  void dispose() {
    //debugPrint('Disposing SlideWidget for ${widget.scheduleItem.name}');

    // Unregister this state instance
    if (widget.key != null) {
      _states.remove(widget.key);
    }

    // Dispose all playlist managers
    for (final entry in _playlistManagers.entries) {
      final id = entry.key;
      final manager = entry.value;

      //debugPrint('Removing listener for playlist $id');

      // Remove listeners first - need to use the same listener reference
      manager.removeListener(() {});
      manager.dispose();
    }

    // Clear the content widgets
    _contentWidgets.clear();

    // Clear the media path cache
    _mediaPathCache.clear();

    // Reset initialization flag
    _isFullyInitialized = false;

    super.dispose();
  }

  /// Parse the slide content from the schedule item
  void _parseSlideContent() {
    //debugPrint('Parsing slide content for ${widget.scheduleItem.name}');

    if (widget.scheduleItem.content == null) {
      //debugPrint('Slide content is null');
      return;
    }

    try {
      // Parse content if it's a string
      final dynamic contentData = widget.scheduleItem.content is String
          ? jsonDecode(widget.scheduleItem.content as String)
          : widget.scheduleItem.content;

      //debugPrint('Content data type: ${contentData.runtimeType}');

      if (contentData is List) {
        //debugPrint('Content data is a list with ${contentData.length} items');

        // Update slide content without setState to avoid rebuild
        _slideContent = contentData;

        // Count the number of playlists
        _totalPlaylists = _slideContent.where((item) =>
            item['type'] == 'playlist'
        ).length;

        //debugPrint('Found $_totalPlaylists playlists in slide content');

        // Create playlist managers
        for (final item in _slideContent) {
          if (item['type'] == 'playlist' &&
              item['content'] != null &&
              item['content']['playlist'] != null) {

            final String id = item['id']?.toString() ?? '';
            final playlist = item['content']['playlist'] as List;

            if (playlist.isEmpty) {
              //debugPrint('Playlist $id is empty');
              continue;
            }

            try {
              final playlistItems = playlist
                  .map((item) => PlaylistItem.fromJson(item))
                  .toList();

              //debugPrint('Creating playlist manager for playlist $id with ${playlistItems.length} items');

              // Pre-fetch ALL item file paths to speed up rendering
              if (playlistItems.isNotEmpty) {
                // Pre-fetch all items in parallel
                for (final item in playlistItems) {
                  widget.campaignController.getMediaFilePath(item.name).then((path) {
                    if (path != null) {
                      // Cache the path but don't log to reduce noise
                    }
                  });
                }
              }

              _playlistManagers[id] = PlaylistManager(
                items: playlistItems,
                onPlaylistComplete: () => _onPlaylistComplete(id),
              );
            } catch (e) {
              //debugPrint('Error creating playlist items for playlist $id: $e');
              // Count this as a completed playlist to avoid getting stuck
              _completedPlaylists++;
            }
          }
        }

        // Safety check: if we couldn't create any playlist managers but there are playlists
        if (_playlistManagers.isEmpty && _totalPlaylists > 0) {
          //debugPrint('No playlist managers created despite having $_totalPlaylists playlists');
          _totalPlaylists = 0; // Reset to trigger the safety check in initState
        }
      } else {
        //debugPrint('Content data is not a list: $contentData');
      }
    } catch (e) {
      //debugPrint('Error parsing slide content: $e');
      // Rethrow to be caught by the initState method
      rethrow;
    }
  }

  /// Get media file path with caching to avoid redundant async operations
  Future<String?> _getMediaFilePathCached(String mediaName) {
    // Check if we already have a future for this media name
    if (!_mediaPathCache.containsKey(mediaName)) {
      // Cache the future to avoid creating a new one each time
      _mediaPathCache[mediaName] = widget.campaignController.getMediaFilePath(mediaName);
    }

    // Return the cached future
    return _mediaPathCache[mediaName]!;
  }

  /// Handle playlist completion
  void _onPlaylistComplete(String playlistId) {
    //debugPrint('Playlist $playlistId completed');

    setState(() {
      _completedPlaylists++;
    });

    //debugPrint('Completed playlists: $_completedPlaylists of $_totalPlaylists');

    // Check if all playlists have completed
    if (_completedPlaylists >= _totalPlaylists) {
      //debugPrint('All playlists completed, calling onComplete');

      // Call onComplete directly
      if (mounted) {
        //debugPrint('Calling onComplete from SlideWidget');
        widget.onComplete();
      }
    }
  }

  /// Check if the widget is fully initialized and ready to display
  bool isFullyInitialized() {
    return _isFullyInitialized && _playlistManagers.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    // Get the actual screen size
    final screenSize = MediaQuery.of(context).size;

    // Get the design resolution from the schedule item
    // If not specified, default to the screen size
    final designWidth = widget.scheduleItem.width?.toDouble() ?? screenSize.width;
    final designHeight = widget.scheduleItem.height?.toDouble() ?? screenSize.height;

    // Calculate scaling factors - we'll use both independently to stretch content
    final horizontalScale = screenSize.width / designWidth;
    final verticalScale = screenSize.height / designHeight;

    //debugPrint('SlideWidget scaling: design=$designWidth x $designHeight, '
        'screen=${screenSize.width} x ${screenSize.height}, '
        'horizontalScale=$horizontalScale, verticalScale=$verticalScale');

    // Parse the background color - default to black (#000000)
    Color backgroundColor = Colors.black;
    if (widget.scheduleItem.bgColor != null && widget.scheduleItem.bgColor!.isNotEmpty) {
      try {
        final bgColor = widget.scheduleItem.bgColor!.trim();

        // Check if it's an rgba color
        if (bgColor.startsWith('rgba(') || bgColor.startsWith('rgb(')) {
          // Parse rgba format: rgba(r, g, b, a) or rgb(r, g, b)
          final values = bgColor
              .replaceAll('rgba(', '')
              .replaceAll('rgb(', '')
              .replaceAll(')', '')
              .split(',')
              .map((s) => s.trim())
              .toList();

          // Ensure we have at least 3 values (r,g,b)
          if (values.length >= 3) {
            final r = int.parse(values[0]);
            final g = int.parse(values[1]);
            final b = int.parse(values[2]);
            final a = values.length > 3 ? double.parse(values[3]) : 1.0;

            backgroundColor = Color.fromRGBO(r, g, b, a);
            //debugPrint('Parsed RGBA color: $r, $g, $b, $a');
          } else {
            //debugPrint('Invalid RGBA color format, using default black: $bgColor');
            backgroundColor = Colors.black; // Default to black
          }
        } else if (bgColor.startsWith('#')) {
          // It's a hex color
          String hexColor = bgColor.replaceAll('#', '');

          // Handle shorthand hex (#RGB) by duplicating each character (#RRGGBB)
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Ensure it's a valid hex color
          if (hexColor.length == 6) {
            final colorValue = int.parse('0xFF$hexColor');
            backgroundColor = Color(colorValue);
            //debugPrint('Parsed hex color: ${backgroundColor.toString()}');
          } else {
            //debugPrint('Invalid hex color format, using default black: $bgColor');
            backgroundColor = Colors.black; // Default to black
          }
        } else {
          // Unknown format, default to black
          //debugPrint('Unknown color format, using default black: $bgColor');
          backgroundColor = Colors.black;
        }
      } catch (e) {
        //debugPrint('Error parsing background color: $e');
        //debugPrint('Color string was: ${widget.scheduleItem.bgColor}');
        //debugPrint('Using default black color');
        backgroundColor = Colors.black; // Explicitly set to black on error
      }
    } else {
      //debugPrint('No background color specified, using default black');
    }

    return Container(
      width: screenSize.width,
      height: screenSize.height,
      color: backgroundColor,
      child: Stack(
        children: [
          // Fill the entire screen with the content
          Positioned.fill(
            child: FittedBox(
              fit: BoxFit.fill, // Use fill to stretch content to fill the space
              alignment: Alignment.center,
              child: SizedBox(
                width: designWidth,
                height: designHeight,
                child: Stack(
                  children: _buildSlideContent(designWidth, designHeight),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void didUpdateWidget(SlideWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the schedule item has changed
    if (oldWidget.scheduleItem.id != widget.scheduleItem.id) {
      //debugPrint('SlideWidget: Schedule item changed, re-parsing content');
      _parseSlideContent();
    }
  }

  /// Build the slide content widgets
  List<Widget> _buildSlideContent(double slideWidth, double slideHeight) {
    final List<Widget> widgets = [];

    // Use a set to track which widgets we've processed in this build cycle
    final Set<String> processedWidgetKeys = {};

    for (final item in _slideContent) {
      final String id = item['id']?.toString() ?? '';
      // Get original coordinates and dimensions from the slide content
      final double x = (item['x'] as num?)?.toDouble() ?? 0;
      final double y = (item['y'] as num?)?.toDouble() ?? 0;
      final double width = (item['width'] as num?)?.toDouble() ?? 100;
      final double height = (item['height'] as num?)?.toDouble() ?? 100;

      if (item['type'] == 'playlist') {
        final playlistManager = _playlistManagers[id];
        if (playlistManager != null) {
          final currentItem = playlistManager.currentItem;

          if (currentItem != null) {
            final String widgetKey = '$id-${playlistManager.currentIndex}';

            // Track that we've processed this widget
            processedWidgetKeys.add(widgetKey);

            // Only log if this is the first time we're building this item in this cycle
            if (!_contentWidgets.containsKey(widgetKey)) {
              //debugPrint('Building content for playlist $id, item ${currentItem.name}');
            }

            Widget contentWidget;

            // Check if we already have a widget for this item
            if (_contentWidgets.containsKey(widgetKey)) {
              // Use cached widget without logging to reduce noise
              contentWidget = _contentWidgets[widgetKey]!;
            } else {
              //debugPrint('Creating new widget for $widgetKey');

              // Create a widget key for stable identity
              final stableKey = ValueKey('content-$widgetKey');

              // Get the file path - use a cached future if possible
              // We're using a synchronous variable to hold the future to avoid creating a new future each time
              final filePath = _getMediaFilePathCached(currentItem.name);

              // Create a new widget based on the file type
              if (currentItem.fileType?.contains('video') == true) {
                contentWidget = FutureBuilder<String?>(
                  key: stableKey,
                  future: filePath,
                  builder: (context, snapshot) {
                    if (snapshot.hasData && snapshot.data != null) {
                      // Only log once per widget creation
                      if (!_contentWidgets.containsKey(widgetKey)) {
                        //debugPrint('Creating video widget for ${currentItem.name} in playlist $id');
                      }

                      return VideoWidget(
                        key: ValueKey('video-$widgetKey'),
                        filePath: snapshot.data!,
                        width: width,
                        height: height,
                        loop: false,
                        onEnded: () {
                          //debugPrint('Video ended in slide: ${currentItem.name}');

                          // Advance playlist directly without microtask
                          if (mounted) {
                            //debugPrint('Advancing playlist after video ended');
                            playlistManager.nextItem();
                          }
                        },
                        onError: (error) {
                          //debugPrint('Error playing video in slide: $error');

                          // Move to the next item on error
                          if (mounted) {
                            //debugPrint('Advancing playlist after video error');
                            playlistManager.nextItem();
                          }
                        },
                      );
                    } else {
                      // Return a black container without loading indicator
                      return Container(
                        width: width,
                        height: height,
                        color: Colors.black,
                      );
                    }
                  },
                );
              } else {
                // Assume it's an image
                contentWidget = FutureBuilder<String?>(
                  key: stableKey,
                  future: filePath,
                  builder: (context, snapshot) {
                    if (snapshot.hasData && snapshot.data != null) {
                      // Only log once per widget creation
                      if (!_contentWidgets.containsKey(widgetKey)) {
                        //debugPrint('Creating image timer widget for ${currentItem.name} in playlist $id');
                      }

                      return ImageTimerWidget(
                        key: ValueKey('image-timer-$widgetKey'),
                        filePath: snapshot.data!,
                        width: width,
                        height: height,
                        durationInSeconds: currentItem.duration > 0 ? currentItem.duration : 5,
                        onComplete: () {
                          //debugPrint('Image timer completed for ${currentItem.name}');

                          // Advance playlist directly without microtask
                          if (mounted) {
                            //debugPrint('Advancing playlist after image timer completed');
                            playlistManager.nextItem();
                          }
                        },
                      );
                    } else {
                      // Return a black container without loading indicator
                      return Container(
                        width: width,
                        height: height,
                        color: Colors.black,
                      );
                    }
                  },
                );
              }

              // Store the widget in the cache
              _contentWidgets[widgetKey] = contentWidget;
            }

            // Position the widget with a stable key
            // Note: We don't apply scaling here because the parent container handles scaling
            widgets.add(
              Positioned(
                key: ValueKey('positioned-$widgetKey'),
                left: x,
                top: y,
                width: width,
                height: height,
                child: contentWidget,
              ),
            );
          } else {
            //debugPrint('No current item for playlist $id');
          }
        } else {
          //debugPrint('No playlist manager for $id');
        }
      }
    }

    // Clean up any widgets that are no longer needed
    // This prevents memory leaks from old widgets
    _contentWidgets.removeWhere((key, _) => !processedWidgetKeys.contains(key));

    return widgets;
  }
}
