import 'dart:convert';
import 'dart:io';

import 'package:signage/core/models/screen.dart';
import 'package:signage/core/storage/storage_service.dart';

/// Model class representing the settings.json file
class Settings {
  final String screenId; // Changed from int to String for UUID
  final String screenName;
  final String code;
  final String? location;
  final String? description;
  final Map<String, dynamic>? metadata;
  final DateTime? registeredAt;
  final String? updateFrequency; // 00:00:00
  final String? updateGuid;
  final String? startTime;
  final String? endTime;
  final DateTime? trialEndsAt;
  final String? subscriptionStatus;

  Settings({
    required this.screenId,
    required this.screenName,
    required this.code,
    this.location,
    this.description,
    this.metadata,
    this.registeredAt,
    this.updateFrequency = "00:00:30", // Default to 15 minutes
    this.updateGuid,
    this.startTime,
    this.endTime,
    this.trialEndsAt,
    this.subscriptionStatus,
  });

  /// Create Settings from a Screen object
  factory Settings.fromScreen(Screen screen) {
    return Settings(
      screenId: screen.id,
      screenName: screen.name,
      code: screen.code,
      location: screen.location,
      description: screen.description,
      metadata: screen.metadata,
      registeredAt: DateTime.now().toUtc(),
      updateGuid: screen.updateGuid, // Added updateGuid field
      startTime: screen.startTime,
      endTime: screen.endTime,
      trialEndsAt: screen.trialEndsAt,
      subscriptionStatus: screen.subscriptionStatus,
    );
  }

  /// Create Settings from a JSON map
  factory Settings.fromJson(Map<String, dynamic> json) {
    return Settings(
      screenId: json['screen_id']?.toString() ?? '', // Ensure screenId is a string
      screenName: json['screen_name'],
      code: json['code']?.toString() ?? '', // Ensure code is a string
      location: json['location'],
      description: json['description'],
      metadata: json['metadata'] != null
          ? (json['metadata'] is String
              ? jsonDecode(json['metadata'])
              : json['metadata'])
          : null,
      registeredAt: json['registered_at'] != null
          ? DateTime.parse(json['registered_at'])
          : null,
      updateFrequency: json['update_frequency'] ?? "00:00:30",
      updateGuid: json['update_guid'],
      startTime: json['start_time']?.toString(),
      endTime: json['end_time']?.toString(),
      trialEndsAt: json['trial_ends_at'] != null
          ? DateTime.parse(json['trial_ends_at'])
          : null,
      subscriptionStatus: json['subscription_status']?.toString(),
    );
  }

  /// Convert Settings to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'screen_id': screenId,
      'screen_name': screenName,
      'code': code,
      'location': location,
      'description': description,
      'metadata': metadata != null
          ? (metadata is String ? metadata : jsonEncode(metadata))
          : null,
      'registered_at': registeredAt?.toIso8601String(),
      'update_frequency': updateFrequency,
      'update_guid': updateGuid,
      'start_time': startTime,
      'end_time': endTime,
      'trial_ends_at': trialEndsAt?.toIso8601String(),
      'subscription_status': subscriptionStatus,
    };
  }

  /// Save settings to the settings.json file
  Future<void> save() async {
    try {
      final signageDir = await StorageService.signageDirectory;
      final settingsFile = File('$signageDir/settings.json');
      final tempFile = File('$signageDir/settings.json.tmp');

      final jsonString = jsonEncode(toJson());

      // Write to temporary file first
      await tempFile.writeAsString(jsonString, flush: true);

      // Ensure data is written to disk
      final randomAccessFile = await tempFile.open(mode: FileMode.append);
      await randomAccessFile.flush();
      await randomAccessFile.close();

      // Verify the temporary file was written correctly
      final verifyContent = await tempFile.readAsString();
      jsonDecode(verifyContent); // This will throw if JSON is invalid

      // If verification passes, replace the original file atomically
      if (await settingsFile.exists()) {
        await settingsFile.delete();
      }
      await tempFile.rename(settingsFile.path);

      // Ensure the renamed file is also flushed to disk
      final finalFile = File('$signageDir/settings.json');
      final finalRandomAccessFile = await finalFile.open(mode: FileMode.append);
      await finalRandomAccessFile.flush();
      await finalRandomAccessFile.close();

      _debugLog('Settings saved to: ${settingsFile.path}');
    } catch (e) {
      _debugLog('Error saving settings: $e');

      // Clean up temporary file if it exists
      try {
        final signageDir = await StorageService.signageDirectory;
        final tempFile = File('$signageDir/settings.json.tmp');
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      } catch (cleanupError) {
        _debugLog('Error cleaning up temp file: $cleanupError');
      }

      rethrow;
    }
  }

  /// Load settings from the settings.json file
  static Future<Settings?> load() async {
    try {
      final signageDir = await StorageService.signageDirectory;
      final settingsFile = File('$signageDir/settings.json');

      if (!await settingsFile.exists()) {
        _debugLog('Settings file does not exist');
        return null;
      }

      final jsonString = await settingsFile.readAsString();

      // Check if the file is empty or contains only whitespace
      if (jsonString.trim().isEmpty) {
        _debugLog('Settings file is empty, deleting corrupted file');
        await settingsFile.delete();
        return null;
      }

      final json = jsonDecode(jsonString);

      return Settings.fromJson(json);
    } catch (e) {
      _debugLog('Error loading settings: $e');

      // If there's a JSON parsing error, the file might be corrupted
      // Delete the corrupted file so it can be recreated
      try {
        final signageDir = await StorageService.signageDirectory;
        final settingsFile = File('$signageDir/settings.json');
        if (await settingsFile.exists()) {
          _debugLog('Deleting corrupted settings file');
          await settingsFile.delete();
        }
      } catch (deleteError) {
        _debugLog('Error deleting corrupted settings file: $deleteError');
      }

      return null;
    }
  }

  /// Helper method for logging
  static void _debugLog(String message) {
    // In a production app, you would use a proper logging framework
    // For now, we'll use print in debug mode only
    assert(() {
      print('[Settings] $message');
      return true;
    }());
  }

  /// Check if registration is needed
  static Future<bool> isRegistrationNeeded() async {
    final settings = await Settings.load();

    // Registration is needed if settings don't exist or code is null
    return settings == null || settings.code.isEmpty;
  }

  @override
  String toString() {
    return 'Settings(screenId: $screenId, screenName: $screenName, code: $code)';
  }
}
