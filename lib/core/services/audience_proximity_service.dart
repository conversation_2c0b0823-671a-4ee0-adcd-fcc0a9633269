import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:signage/core/services/serial_communication_service.dart';
import 'package:signage/core/storage/storage_service.dart';

/// Service for managing audience proximity detection and sensor monitoring
class AudienceProximityService {
  static final AudienceProximityService _instance = AudienceProximityService._internal();
  factory AudienceProximityService() => _instance;
  AudienceProximityService._internal();

  // Serial communication service
  SerialCommunicationService? _serialService;

  // State management
  bool _isInitialized = false;

  // Distance monitoring
  StreamSubscription<LD2410Data>? _dataSubscription;
  double _currentProximityDistance = 0.0; // Global variable to store current distance

  // Callback for sensor data updates
  Function(double)? _readSensorDataCallback;

  // Getters
  bool get isInitialized => _isInitialized;
  double get currentProximityDistance => _currentProximityDistance;

  /// Initialize the audience proximity service
  Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint('AudienceProximityService: Already initialized');
      return _serialService?.isConnected ?? false;
    }

    debugPrint('AudienceProximityService: Initializing');

    try {
      // Read campaigns.json file to check for trigger_type == 4
      final hasProximityCampaigns = await _checkForProximityCampaigns();

      if (!hasProximityCampaigns) {
        debugPrint('AudienceProximityService: No proximity campaigns found, skipping serial initialization');
        _isInitialized = true;
        return false;
      }

      debugPrint('AudienceProximityService: Proximity campaigns found, initializing serial service');

      // Initialize serial service
      _serialService = SerialCommunicationService();
      final serialInitialized = await _serialService!.initialize();

      if (serialInitialized) {
        debugPrint('AudienceProximityService: Serial service initialized successfully');
        // Start distance monitoring
        _startDistanceMonitoring();
        _isInitialized = true;
        return true;
      } else {
        debugPrint('AudienceProximityService: Serial service initialization failed');
        _isInitialized = true;
        return false;
      }
    } catch (e) {
      debugPrint('AudienceProximityService: Initialization error: $e');
      _isInitialized = true;
      return false;
    }
  }

  /// Check if campaigns.json contains any campaign with trigger_type == 4
  Future<bool> _checkForProximityCampaigns() async {
    try {
      final signageDir = await StorageService.signageDirectory;
      final dataDir = '$signageDir/data';
      final campaignsFile = File('$dataDir/campaigns.json');

      if (!await campaignsFile.exists()) {
        debugPrint('AudienceProximityService: campaigns.json file not found');
        return false;
      }

      final campaignsJson = await campaignsFile.readAsString();
      final campaignsData = jsonDecode(campaignsJson) as List;

      // Check if any campaign has trigger_type == 4
      for (final campaignData in campaignsData) {
        final triggerType = campaignData['trigger_type'];
        if (triggerType == 4) {
          debugPrint('AudienceProximityService: Found proximity campaign: ${campaignData['name']}');
          return true;
        }
      }

      debugPrint('AudienceProximityService: No proximity campaigns found');
      return false;
    } catch (e) {
      debugPrint('AudienceProximityService: Error checking for proximity campaigns: $e');
      return false;
    }
  }

  /// Start monitoring sensor data from the LD2410B sensor
  void _startDistanceMonitoring() {
    debugPrint('AudienceProximityService: Starting distance monitoring');

    if (_serialService?.dataStream == null) {
      debugPrint('AudienceProximityService: No data stream available');
      return;
    }

    _dataSubscription = _serialService!.dataStream!.listen(
      (sensorData) {
        _handleSensorDataUpdate(sensorData);
      },
      onError: (error) {
        debugPrint('AudienceProximityService: Sensor data stream error: $error');
      },
    );

    debugPrint('AudienceProximityService: Distance monitoring subscription created successfully');
  }

  /// Handle sensor data updates from the LD2410B sensor
  void _handleSensorDataUpdate(LD2410Data sensorData) {
    // Convert distance from centimeters to meters and store in global variable
    _currentProximityDistance = sensorData.detectionDistance / 100.0;
    
    // Call the callback function if it's set
    _readSensorDataCallback?.call(_currentProximityDistance);
  }

  /// Set the callback function for sensor data updates
  void setReadSensorDataCallback(Function(double) callback) {
    _readSensorDataCallback = callback;
  }

  /// Check if serial communication is connected
  bool isSerialConnected() {
    return _serialService?.isConnected ?? false;
  }

  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    debugPrint('AudienceProximityService: Disposing');

    await _dataSubscription?.cancel();
    _dataSubscription = null;

    await _serialService?.dispose();

    _isInitialized = false;
    _currentProximityDistance = 0.0;
    _readSensorDataCallback = null;
  }
}
