import 'package:flutter/foundation.dart';
import 'package:signage/core/models/schedule_item.dart';

/// Manager for handling playlist content
class PlaylistManager with ChangeNotifier {
  /// The playlist items
  final List<PlaylistItem> _items;

  /// The current index in the playlist
  int _currentIndex = 0;

  /// Callback when the playlist completes
  final void Function()? onPlaylistComplete;

  /// Creates a PlaylistManager
  PlaylistManager({
    required List<PlaylistItem> items,
    this.onPlaylistComplete,
  }) : _items = items;

  /// Get the current playlist item
  PlaylistItem? get currentItem =>
      _items.isNotEmpty && _currentIndex < _items.length
          ? _items[_currentIndex]
          : null;

  /// Get the current index
  int get currentIndex => _currentIndex;

  /// Get the total number of items
  int get itemCount => _items.length;

  /// Check if this is the last item in the playlist
  bool get isLastItem => _currentIndex >= _items.length - 1;

  /// Move to the next item in the playlist
  void nextItem() {
    if (_items.isEmpty) {
      //debugPrint('Playlist is empty, cannot move to next item');
      return;
    }

    final oldIndex = _currentIndex;
    final currentItemName = currentItem?.name ?? 'unknown';

    if (_currentIndex < _items.length - 1) {
      _currentIndex++;
      final newItemName = _items[_currentIndex].name;
      //debugPrint('Moving from playlist item $oldIndex ($currentItemName) to $_currentIndex ($newItemName)');
      notifyListeners();
    } else {
      // This is the last item
      //debugPrint('Reached end of playlist, resetting to beginning and calling onPlaylistComplete');

      // Reset to the beginning
      _currentIndex = 0;

      // Notify that the playlist has completed
      if (onPlaylistComplete != null) {
        //debugPrint('Calling onPlaylistComplete callback');

        // Use Future.microtask to ensure the callback is called after the current frame
        Future.microtask(() {
          onPlaylistComplete!();
        });
      }

      notifyListeners();
    }
  }

  /// Reset the playlist to the beginning
  void reset() {
    _currentIndex = 0;
    notifyListeners();
  }
}
