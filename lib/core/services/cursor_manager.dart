import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signage/utils/platform_utils.dart';

/// A service to manage cursor visibility and positioning on desktop platforms
class CursorManager {
  static CursorManager? _instance;
  static CursorManager get instance => _instance ??= CursorManager._();

  CursorManager._();

  // Method channel for cursor positioning
  static const MethodChannel _channel = MethodChannel('com.app.signage/cursor');

  bool _isCursorVisible = true;
  bool _isInitialized = false;

  /// Initialize the cursor manager
  /// This should be called when the player screen loads
  void initialize() {
    if (_isInitialized || !PlatformUtils.isDesktop) {
      return;
    }

    _isInitialized = true;

    // Position cursor at top-left (0,0) first
    positionCursorToTopLeft();

    // Then hide cursor by default on desktop platforms
    hideCursor();
  }

  /// Hide the mouse cursor
  void hideCursor() {
    if (!PlatformUtils.isDesktop) {
      return;
    }

    if (_isCursorVisible) {
      //debugPrint('CursorManager: Hiding cursor');
      _isCursorVisible = false;
      
      // Use SystemChrome to hide cursor
      SystemChrome.setSystemUIChangeCallback((systemOverlaysAreVisible) async {
        // This callback is called when system UI visibility changes
        // We can use this to maintain cursor state
        return;
      });
      
      // Note: Flutter doesn't have a direct API to hide the mouse cursor
      // We'll use a workaround by setting the cursor to transparent
      // This will be handled in the widget level
    }
  }

  /// Show the mouse cursor
  void showCursor() {
    if (!PlatformUtils.isDesktop) {
      return;
    }

    if (!_isCursorVisible) {
      //debugPrint('CursorManager: Showing cursor');
      _isCursorVisible = true;
    }
  }

  /// Position cursor to top-left corner (0,0)
  Future<void> positionCursorToTopLeft() async {
    if (!PlatformUtils.isDesktop) {
      return;
    }

    try {
      //debugPrint('CursorManager: Positioning cursor to (0,0)');
      await _channel.invokeMethod('positionCursor', {'x': 0, 'y': 0});
    } catch (e) {
      //debugPrint('CursorManager: Failed to position cursor: $e');
    }
  }

  /// Get the current cursor visibility state
  bool get isCursorVisible => _isCursorVisible;

  /// Dispose the cursor manager
  void dispose() {
    _isInitialized = false;
    _isCursorVisible = true;
  }
}
