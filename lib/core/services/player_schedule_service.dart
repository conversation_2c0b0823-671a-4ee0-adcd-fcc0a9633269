import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/utils/time_utils.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

/// Service to manage player on/off schedule based on start and end times
class PlayerScheduleService {
  /// Callback when content should start playing
  final VoidCallback? onStartContent;

  /// Callback when content should stop playing (show black screen)
  final VoidCallback? onStopContent;

  /// Current timer for scheduling
  Timer? _currentTimer;

  /// Whether the service is currently running
  bool _isRunning = false;

  /// Whether content is currently being displayed
  bool _isContentDisplaying = false;

  /// Current settings
  Settings? _settings;

  /// Constructor
  PlayerScheduleService({
    this.onStartContent,
    this.onStopContent,
  });

  /// Initialize the service with settings
  Future<void> initialize(Settings? settings) async {
    _settings = settings;

    if (_settings == null) {
      //debugPrint('PlayerScheduleService: No settings provided, content will play continuously');
      _startContentDisplay();
      return;
    }

    final startTime = _settings!.startTime;
    final endTime = _settings!.endTime;

    // Check if both start and end times are provided
    if (startTime == null || endTime == null) {
      //debugPrint('PlayerScheduleService: Start time or end time is null, content will play continuously');
      _startContentDisplay();
      return;
    }

    // Parse the time strings
    final parsedStartTime = TimeUtils.parseTimeString(startTime);
    final parsedEndTime = TimeUtils.parseTimeString(endTime);

    if (parsedStartTime == null || parsedEndTime == null) {
      //debugPrint('PlayerScheduleService: Invalid time format, content will play continuously');
      //debugPrint('Start time: $startTime, End time: $endTime');
      _startContentDisplay();
      return;
    }

    //debugPrint('PlayerScheduleService: Parsed start time: ${TimeUtils.formatTimeString(parsedStartTime)}');
    //debugPrint('PlayerScheduleService: Parsed end time: ${TimeUtils.formatTimeString(parsedEndTime)}');

    // Determine current state and set up appropriate timer
    _determineCurrentStateAndSetTimer(parsedStartTime, parsedEndTime);
  }

  /// Determine current state based on time and set up appropriate timer
  void _determineCurrentStateAndSetTimer(DateTime startTime, DateTime endTime) {
    final now = TimeUtils.getCurrentTime();

    //debugPrint('PlayerScheduleService: Current time: ${TimeUtils.formatTimeString(now)}');
    //debugPrint('PlayerScheduleService: Start time: ${TimeUtils.formatTimeString(startTime)}');
    //debugPrint('PlayerScheduleService: End time: ${TimeUtils.formatTimeString(endTime)}');

    if (TimeUtils.isCurrentTimeBetween(startTime, endTime)) {
      // Current time is between start and end time - display content
      //debugPrint('PlayerScheduleService: Current time is between start and end time - starting content display');
      _startContentDisplay();

      // Set timer to stop content at end time
      final durationUntilEnd = TimeUtils.calculateDurationUntilToday(endTime);
      if (durationUntilEnd.isNegative) {
        // End time is tomorrow
        final durationUntilEndTomorrow = TimeUtils.calculateDurationUntilNextDay(endTime);
        _setStopTimer(durationUntilEndTomorrow);
      } else {
        _setStopTimer(durationUntilEnd);
      }

    } else if (TimeUtils.isCurrentTimeBefore(startTime)) {
      // Current time is before start time - show black screen
      //debugPrint('PlayerScheduleService: Current time is before start time - showing black screen');
      _stopContentDisplay();

      // Set timer to start content at start time
      final durationUntilStart = TimeUtils.calculateDurationUntilToday(startTime);
      _setStartTimer(durationUntilStart);

    } else if (TimeUtils.isCurrentTimeAfter(endTime)) {
      // Current time is after end time - show black screen
      //debugPrint('PlayerScheduleService: Current time is after end time - showing black screen');
      _stopContentDisplay();

      // Set timer to start content at start time tomorrow
      final durationUntilStartTomorrow = TimeUtils.calculateDurationUntilNextDay(startTime);
      _setStartTimer(durationUntilStartTomorrow);
    }
  }

  /// Set timer to start content display
  void _setStartTimer(Duration duration) {
    _cancelCurrentTimer();

    final totalSeconds = duration.inSeconds;
    //debugPrint('PlayerScheduleService: Setting start timer for $totalSeconds seconds (${_formatDuration(duration)})');

    _currentTimer = Timer(duration, () {
      //debugPrint('PlayerScheduleService: Start timer expired - starting content display');
      _startContentDisplay();

      // After starting content, set timer to stop at end time
      if (_settings != null && _settings!.endTime != null) {
        final endTime = TimeUtils.parseTimeString(_settings!.endTime!);
        if (endTime != null) {
          final durationUntilEnd = TimeUtils.calculateDurationUntilToday(endTime);
          _setStopTimer(durationUntilEnd);
        }
      }
    });
  }

  /// Set timer to stop content display
  void _setStopTimer(Duration duration) {
    _cancelCurrentTimer();

    final totalSeconds = duration.inSeconds;
    //debugPrint('PlayerScheduleService: Setting stop timer for $totalSeconds seconds (${_formatDuration(duration)})');

    _currentTimer = Timer(duration, () {
      //debugPrint('PlayerScheduleService: Stop timer expired - stopping content display');
      _stopContentDisplay();

      // After stopping content, set timer to start at start time tomorrow
      if (_settings != null && _settings!.startTime != null) {
        final startTime = TimeUtils.parseTimeString(_settings!.startTime!);
        if (startTime != null) {
          final durationUntilStartTomorrow = TimeUtils.calculateDurationUntilNextDay(startTime);
          _setStartTimer(durationUntilStartTomorrow);
        }
      }
    });
  }

  /// Start content display
  void _startContentDisplay() {
    if (!_isContentDisplaying) {
      _isContentDisplaying = true;
      //debugPrint('PlayerScheduleService: Starting content display');

      // Enable wake lock to keep screen alive
      try {
        WakelockPlus.enable();
      } catch (e) {
        //debugPrint('PlayerScheduleService: Failed to enable wakelock: $e');
      }

      // Call the callback
      onStartContent?.call();
    }
  }

  /// Stop content display (show black screen)
  void _stopContentDisplay() {
    _isContentDisplaying = false;
    //debugPrint('PlayerScheduleService: Stopping content display');

    // Disable wake lock
    try {
      WakelockPlus.disable();
    } catch (e) {
      //debugPrint('PlayerScheduleService: Failed to disable wakelock: $e');
    }

    // Always call the callback to ensure UI is updated
    onStopContent?.call();
  }

  /// Cancel current timer
  void _cancelCurrentTimer() {
    _currentTimer?.cancel();
    _currentTimer = null;
  }

  /// Format duration for logging
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    return '${hours}h ${minutes}m ${seconds}s';
  }

  /// Start the service
  void start() {
    if (!_isRunning) {
      _isRunning = true;
      //debugPrint('PlayerScheduleService: Service started');
    }
  }

  /// Stop the service
  void stop() {
    if (_isRunning) {
      _isRunning = false;
      _cancelCurrentTimer();

      // Disable wake lock
      try {
        WakelockPlus.disable();
      } catch (e) {
        //debugPrint('PlayerScheduleService: Failed to disable wakelock on stop: $e');
      }

      //debugPrint('PlayerScheduleService: Service stopped');
    }
  }

  /// Get current content display state
  bool get isContentDisplaying => _isContentDisplaying;

  /// Dispose the service
  void dispose() {
    stop();
  }
}
