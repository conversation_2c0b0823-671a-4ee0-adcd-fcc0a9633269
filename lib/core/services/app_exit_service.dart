import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:signage/utils/platform_utils.dart';

/// Service for handling app exit
class AppExitService {
  // Method channel for communication with native code
  static const MethodChannel _channel = MethodChannel('com.app.signage/system_buttons');

  /// Exit the app completely
  static Future<void> exitApp() async {
    //debugPrint('AppExitService: Exiting app on platform: ${PlatformUtils.platformName}');

    if (PlatformUtils.isAndroid) {
      // Try multiple methods to ensure the app exits completely on Android

      // First try to use the method channel to exit the app
      try {
        //debugPrint('AppExitService: Trying method channel to exit app');
        await _channel.invokeMethod('exitApp');
      } catch (e) {
        //debugPrint('AppExitService: Error using method channel to exit app: $e');

        // Try System.exit as a fallback
        try {
          //debugPrint('AppExitService: Trying SystemNavigator.pop');
          await SystemNavigator.pop(animated: true);
        } catch (e) {
          //debugPrint('AppExitService: Error using SystemNavigator.pop: $e');

          // Last resort, try to exit using Platform.exit
          try {
            //debugPrint('AppExitService: Trying exit(0)');
            exit(0);
          } catch (e) {
            //debugPrint('AppExitService: Error using exit(0): $e');
          }
        }
      }
    } else if (PlatformUtils.isDesktop) {
      // On desktop platforms, use exit
      //debugPrint('AppExitService: Exiting desktop app with exit(0)');
      exit(0);
    } else {
      // For other platforms, use system navigator
      //debugPrint('AppExitService: Using SystemNavigator.pop for other platforms');
      await SystemNavigator.pop(animated: true);
    }
  }
}
