import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signage/core/models/campaign.dart';
import 'package:signage/core/services/serial_communication_service.dart';

/// Service for managing audience proximity detection and campaign triggering
class AudienceProximityService {
  static final AudienceProximityService _instance = AudienceProximityService._internal();
  factory AudienceProximityService() => _instance;
  AudienceProximityService._internal();

  // Serial communication service
  final SerialCommunicationService _serialService = SerialCommunicationService();
  
  // State management
  bool _isInitialized = false;
  bool _playingAudienceProximity = false;
  
  List<Campaign> _proximityCampaigns = [];
  int _currentProximityCampaignIndex = 0;
  
  // Distance monitoring
  StreamSubscription<LD2410Data>? _dataSubscription;
  LD2410Data? _lastSensorData;
  int? _lastDistance;
  double? _currentThreshold;
  
  // Callbacks
  Function(Campaign campaign)? _onStartProximityCampaign;
  Function()? _onEndProximityCampaign;
  Function(Campaign campaign)? _onNextProximityCampaign;
  Function(Campaign campaign)? _onImmediateProximityTransition;

  /// Get the current proximity playing state
  bool get playingAudienceProximity => _playingAudienceProximity;

  /// Get the current proximity campaigns
  List<Campaign> get proximityCampaigns => _proximityCampaigns;

  /// Get the current proximity campaign index
  int get currentProximityCampaignIndex => _currentProximityCampaignIndex;

  /// Initialize the audience proximity service
  Future<bool> initialize({
    required List<Campaign> campaigns,
    Function(Campaign campaign)? onStartProximityCampaign,
    Function()? onEndProximityCampaign,
    Function(Campaign campaign)? onNextProximityCampaign,
    Function(Campaign campaign)? onImmediateProximityTransition,
  }) async {
    if (_isInitialized) {
      //debugPrint('AudienceProximityService: Already initialized');
      return true;
    }

    //debugPrint('AudienceProximityService: Initializing');

    // Store callbacks
    _onStartProximityCampaign = onStartProximityCampaign;
    _onEndProximityCampaign = onEndProximityCampaign;
    _onNextProximityCampaign = onNextProximityCampaign;
    _onImmediateProximityTransition = onImmediateProximityTransition;

    // Filter campaigns with trigger_type = 4
    _proximityCampaigns = campaigns.where((campaign) => campaign.triggerType == 4).toList();
    
    //debugPrint('AudienceProximityService: Found ${_proximityCampaigns.length} proximity campaigns');

    // If no proximity campaigns, don't initialize serial communication
    if (_proximityCampaigns.isEmpty) {
      //debugPrint('AudienceProximityService: No proximity campaigns found, skipping serial initialization');
      _isInitialized = true;
      return true;
    }

    // Initialize serial communication
    bool serialInitialized = await _serialService.initialize();
    if (!serialInitialized) {
      //debugPrint('AudienceProximityService: Failed to initialize serial communication');
      _isInitialized = true; // Still mark as initialized to prevent retries
      return false;
    }

    // Start listening to distance data
    _startDistanceMonitoring();

    _isInitialized = true;
    //debugPrint('AudienceProximityService: Initialization completed successfully');
    return true;
  }

  /// Start monitoring sensor data from the LD2410B sensor
  void _startDistanceMonitoring() {
    //debugPrint('AudienceProximityService: Checking data stream availability...');
    //debugPrint('AudienceProximityService: Serial service connected: ${_serialService.isConnected}');
    //debugPrint('AudienceProximityService: Data stream null: ${_serialService.dataStream == null}');

    if (_serialService.dataStream == null) {
      //debugPrint('AudienceProximityService: No data stream available');
      return;
    }

    //debugPrint('AudienceProximityService: Starting distance monitoring');

    _dataSubscription = _serialService.dataStream!.listen(
      (sensorData) {
        //debugPrint('AudienceProximityService: Received sensor data: $sensorData');
        _handleSensorDataUpdate(sensorData);
      },
      onError: (error) {
        //debugPrint('AudienceProximityService: Sensor data stream error: $error');
      },
    );

    //debugPrint('AudienceProximityService: Distance monitoring subscription created successfully');
  }

  /// Handle sensor data updates from the LD2410B sensor
  void _handleSensorDataUpdate(LD2410Data sensorData) {

    _lastSensorData = sensorData;
    _lastDistance = sensorData.detectionDistance;

    // Convert distance from centimeters to meters for comparison
    double distanceInMeters = sensorData.detectionDistance / 100.0;

    // Enhanced logging with full sensor data (log significant changes)
    if (_lastDistance == null || (_lastDistance! - sensorData.detectionDistance).abs() > 5) {
      //debugPrint('AudienceProximityService: Sensor data update - $sensorData');
      //debugPrint('AudienceProximityService: Distance: ${distanceInMeters}m (${sensorData.detectionDistance}cm), threshold: ${_currentThreshold}m');
    }

    // Check if we have proximity campaigns and a valid threshold
    if (_proximityCampaigns.isEmpty) return;

    // Find the minimum distance threshold from all proximity campaigns
    double? minThreshold;
    for (final campaign in _proximityCampaigns) {
      if (campaign.distance != null && campaign.distance! > 0) {
        if (minThreshold == null || campaign.distance! < minThreshold) {
          minThreshold = campaign.distance!;
        }
      }
    }

    if (minThreshold == null) {
      //debugPrint('AudienceProximityService: No valid distance thresholds found');
      return;
    }

    _currentThreshold = minThreshold;

    // Check proximity state (compare meters to meters)
    bool isWithinThreshold = distanceInMeters <= _currentThreshold!;
    debugPrint('AudienceProximityService: Person within threshold: $isWithinThreshold');

    if (isWithinThreshold && !_playingAudienceProximity) {
      debugPrint('AudienceProximityService: Proximity detected! Distance: ${distanceInMeters}m <= ${_currentThreshold}m');
      // Proximity detected - immediately trigger transition to proximity campaign
      //debugPrint('AudienceProximityService: Proximity detected! Distance: ${distanceInMeters}m <= ${_currentThreshold}m');
      //debugPrint('AudienceProximityService: Detection state: ${sensorData.detectionState.name}, Moving: ${sensorData.movingTargetDistance}cm, Static: ${sensorData.staticTargetDistance}cm');
      _triggerImmediateProximityTransition();
    } else if (!isWithinThreshold && _playingAudienceProximity) {
      // Proximity lost - continue current campaign but mark for ending after completion
      //debugPrint('AudienceProximityService: Proximity lost (${distanceInMeters}m > ${_currentThreshold}m), will end proximity mode after current campaign completes');
      // Note: The actual ending will be handled in _handleProximitySequenceCompletion()
    } else if (isWithinThreshold && _playingAudienceProximity) {
      // Proximity continues - log for monitoring
      //debugPrint('AudienceProximityService: Proximity continues (${distanceInMeters}m <= ${_currentThreshold}m), proximity mode active');
    } else {
      // No proximity and not playing proximity campaigns - normal state
      //debugPrint('AudienceProximityService: No proximity detected (${distanceInMeters}m > ${_currentThreshold}m), normal mode');
    }
    //print _personFound
    

  }

  /// Trigger immediate transition to proximity campaign
  void _triggerImmediateProximityTransition() {
    //debugPrint('AudienceProximityService: Triggering immediate proximity transition');

    _playingAudienceProximity = true;
    _currentProximityCampaignIndex = 0;

    if (_proximityCampaigns.isNotEmpty) {
      final firstCampaign = _proximityCampaigns[_currentProximityCampaignIndex];
      //debugPrint('AudienceProximityService: Immediately switching to proximity campaign: ${firstCampaign.name}');

      // Call the immediate transition callback to interrupt current campaign
      _onImmediateProximityTransition?.call(firstCampaign);
    }
  }

  /// Start the proximity campaign sequence
  void _startProximitySequence() {
    //debugPrint('AudienceProximityService: Starting proximity campaign sequence');

    _playingAudienceProximity = true;
    _currentProximityCampaignIndex = 0;

    if (_proximityCampaigns.isNotEmpty) {
      final firstCampaign = _proximityCampaigns[_currentProximityCampaignIndex];
      //debugPrint('AudienceProximityService: Starting first proximity campaign: ${firstCampaign.name}');
      _onStartProximityCampaign?.call(firstCampaign);
    }
  }

  /// Move to the next proximity campaign
  void moveToNextProximityCampaign() {
    if (!_playingAudienceProximity || _proximityCampaigns.isEmpty) {
      return;
    }

    _currentProximityCampaignIndex++;

    if (_currentProximityCampaignIndex >= _proximityCampaigns.length) {
      // Completed all proximity campaigns - check if we should continue or end
      _handleProximitySequenceCompletion();
    } else {
      // Move to next proximity campaign
      final nextCampaign = _proximityCampaigns[_currentProximityCampaignIndex];
      //debugPrint('AudienceProximityService: Moving to next proximity campaign: ${nextCampaign.name}');
      _onNextProximityCampaign?.call(nextCampaign);
    }
  }

  /// Handle completion of proximity campaign sequence with distance validation
  void _handleProximitySequenceCompletion() {
    //debugPrint('AudienceProximityService: ===== HANDLING PROXIMITY SEQUENCE COMPLETION =====');
    //debugPrint('AudienceProximityService: Current state - playingAudienceProximity: $_playingAudienceProximity');
    //debugPrint('AudienceProximityService: Proximity campaigns count: ${_proximityCampaigns.length}');

    // Check current distance to determine if we should continue or end proximity mode
    if (_lastSensorData != null && _currentThreshold != null) {
      double currentDistanceInMeters = _lastSensorData!.detectionDistance / 100.0;
      bool stillWithinThreshold = currentDistanceInMeters <= _currentThreshold!;

      //debugPrint('AudienceProximityService: Distance validation - current: ${currentDistanceInMeters}m, threshold: ${_currentThreshold}m, within: $stillWithinThreshold');

      if (stillWithinThreshold) {
        // Someone is still within threshold - restart proximity campaign sequence
        //debugPrint('AudienceProximityService: ✓ Person still within threshold, restarting proximity campaigns');
        _currentProximityCampaignIndex = 0;

        if (_proximityCampaigns.isNotEmpty) {
          final firstCampaign = _proximityCampaigns[_currentProximityCampaignIndex];
          //debugPrint('AudienceProximityService: ✓ Restarting with first proximity campaign: ${firstCampaign.name}');
          _onNextProximityCampaign?.call(firstCampaign);
        }
        //debugPrint('AudienceProximityService: ===== PROXIMITY SEQUENCE RESTARTED =====');
        return;
      } else {
        //debugPrint('AudienceProximityService: ✗ Person no longer within threshold, ending proximity mode');
      }
    } else {
      //debugPrint('AudienceProximityService: ✗ No sensor data or threshold available');
    }

    // No one within threshold or no sensor data - end proximity sequence
    //debugPrint('AudienceProximityService: ===== ENDING PROXIMITY SEQUENCE =====');
    _endProximitySequence();
  }

  /// End the proximity campaign sequence
  void _endProximitySequence() {
    //debugPrint('AudienceProximityService: ===== ENDING PROXIMITY CAMPAIGN SEQUENCE =====');
    //debugPrint('AudienceProximityService: Setting playingAudienceProximity = false');
    //debugPrint('AudienceProximityService: Resetting campaign index to 0');

    _playingAudienceProximity = false;
    _currentProximityCampaignIndex = 0;

    //debugPrint('AudienceProximityService: Calling onEndProximityCampaign callback');
    _onEndProximityCampaign?.call();
    //debugPrint('AudienceProximityService: ===== PROXIMITY SEQUENCE ENDED - RETURNING TO NORMAL CAMPAIGNS =====');
  }

  /// Force end proximity sequence (for external control)
  void forceEndProximitySequence() {
    if (_playingAudienceProximity) {
      _endProximitySequence();
    }
  }

  /// Update campaigns list (called when campaigns are refreshed)
  void updateCampaigns(List<Campaign> campaigns) {
    //debugPrint('AudienceProximityService: Updating campaigns');
    
    // Filter campaigns with trigger_type = 4
    _proximityCampaigns = campaigns.where((campaign) => campaign.triggerType == 4).toList();
    
    //debugPrint('AudienceProximityService: Updated to ${_proximityCampaigns.length} proximity campaigns');

    // If we were playing proximity campaigns but now have none, end the sequence
    if (_playingAudienceProximity && _proximityCampaigns.isEmpty) {
      _endProximitySequence();
    }

    // Reset campaign index if it's out of bounds
    if (_currentProximityCampaignIndex >= _proximityCampaigns.length) {
      _currentProximityCampaignIndex = 0;
    }
  }

  /// Get the current distance reading
  int? getCurrentDistance() {
    return _lastDistance;
  }

  /// Get the current distance threshold
  double? getCurrentThreshold() {
    return _currentThreshold;
  }

  /// Check if proximity campaigns are available
  bool hasProximityCampaigns() {
    return _proximityCampaigns.isNotEmpty;
  }

  /// Check if serial communication is connected
  bool isSerialConnected() {
    return _serialService.isConnected;
  }

  /// Check if current distance is within any proximity campaign threshold
  bool isCurrentDistanceWithinThreshold() {
    if (_lastSensorData == null || _proximityCampaigns.isEmpty) {
      return false;
    }

    double currentDistanceInMeters = _lastSensorData!.detectionDistance / 100.0;

    // Check against all proximity campaigns to see if any threshold is met
    for (final campaign in _proximityCampaigns) {
      if (campaign.distance != null && campaign.distance! > 0) {
        if (currentDistanceInMeters <= campaign.distance!) {
          //debugPrint('AudienceProximityService: Current distance ${currentDistanceInMeters}m is within campaign ${campaign.name} threshold ${campaign.distance}m');
          return true;
        }
      }
    }

    //debugPrint('AudienceProximityService: Current distance ${currentDistanceInMeters}m is not within any campaign threshold');
    return false;
  }

  /// Get the current sensor distance in meters (for debugging/monitoring)
  double? getCurrentDistanceInMeters() {
    if (_lastSensorData == null) return null;
    return _lastSensorData!.detectionDistance / 100.0;
  }

  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    //debugPrint('AudienceProximityService: Disposing');

    await _dataSubscription?.cancel();
    _dataSubscription = null;

    await _serialService.dispose();

    _isInitialized = false;
    _playingAudienceProximity = false;
    _proximityCampaigns.clear();
    _currentProximityCampaignIndex = 0;
    _lastDistance = null;
    _lastSensorData = null;
    _currentThreshold = null;

    _onStartProximityCampaign = null;
    _onEndProximityCampaign = null;
    _onNextProximityCampaign = null;
    _onImmediateProximityTransition = null;
  }
}
